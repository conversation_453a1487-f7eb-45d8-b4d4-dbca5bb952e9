.scroll-wrapper {
    overflow: hidden !important;
    padding: 0 !important;
    position: relative
}

.scroll-wrapper > .scroll-content {
    border: none !important;
    box-sizing: content-box !important;
    height: auto;
    left: 0;
    margin: 0;
    max-height: none;
    max-width: none !important;
    overflow: scroll !important;
    padding: 0;
    position: relative !important;
    top: 0;
    width: auto !important
}

.scroll-wrapper > .scroll-content::-webkit-scrollbar {
    height: 0;
    width: 0
}

.scroll-element {
    display: none
}

.scroll-element, .scroll-element div {
    box-sizing: content-box
}

.scroll-element.scroll-x.scroll-scrollx_visible, .scroll-element.scroll-y.scroll-scrolly_visible {
    display: block
}

.scroll-element .scroll-arrow, .scroll-element .scroll-bar {
    cursor: default
}

.scroll-textarea {
    border: 1px solid #ccc;
    border-top-color: #999
}

.scroll-textarea > .scroll-content {
    overflow: hidden !important
}

.scroll-textarea > .scroll-content > textarea {
    border: none !important;
    box-sizing: border-box;
    height: 100% !important;
    margin: 0;
    max-height: none !important;
    max-width: none !important;
    overflow: scroll !important;
    outline: 0;
    padding: 2px;
    position: relative !important;
    top: 0;
    width: 100% !important
}

.scroll-textarea > .scroll-content > textarea::-webkit-scrollbar {
    height: 0;
    width: 0
}

.scrollbar-light > .scroll-element, .scrollbar-light > .scroll-element div {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10
}

.scrollbar-light > .scroll-element {
}

.scrollbar-light > .scroll-element div {
    display: block;
    height: 100%;
    left: 5px;
    top: 0;
    width: 1px
}

.scrollbar-light > .scroll-element .scroll-element_outer {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light > .scroll-element .scroll-element_size {
    background: #dbdbdb;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2RiZGJkYiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNlOGU4ZTgiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -webkit-gradient(linear, left top, right top, color-stop(0, #dbdbdb), color-stop(100%, #e8e8e8));
    background: -webkit-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -o-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -ms-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: linear-gradient(to right, #dbdbdb 0, #e8e8e8 100%);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light > .scroll-element.scroll-x {
    bottom: 0;
    height: 17px;
    left: 0;
    min-width: 100%;
    width: 100%
}

.scrollbar-light > .scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 2px;
    top: 0;
    width: 12px;
    opacity: 0
}

.scrollbar-light > .scroll-element .scroll-bar {
    background: #fefefe;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZlZmVmZSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmNWY1ZjUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -webkit-gradient(linear, left top, right top, color-stop(0, #fefefe), color-stop(100%, #f5f5f5));
    background: -webkit-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -o-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -ms-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: linear-gradient(to right, #fefefe 0, #f5f5f5 100%);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light > .scroll-element.scroll-y .scroll-bar {
    left: 0;
    min-height: 10px;
    width: 11px;
    background: #FFF;
    opacity: 1;
    transition: opacity .25s;
    border:1px solid #DDD;
    box-sizing: border-box;
}

.scrollbar-light > .scroll-element.scroll-y .scroll-bar:hover {
    opacity: 1
}

.scrollbar-light > .scroll-element.scroll-x .scroll-element_size {
    left: -4px;
    width: 1px
}

.scrollbar-light > .scroll-element.scroll-y .scroll-element_outer {
    left: 0;
    top: 2px;
    width: 12px
}

.scrollbar-light > .scroll-element.scroll-y .scroll-element_size {
    top: -4px
}

.scrollbar-light > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {
    left: -19px
}

.scrollbar-light > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {
    top: -19px
}

.scrollbar-light > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -19px
}

.scrollbar-light > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -19px
}