(()=>{"use strict";const e=window.React,t=window.wp.i18n,s=window.wp.blockEditor,l=window.wp.components,r=r=>{const{attributes:a,setAttributes:n,context:i}=r,o=(0,s.useBlockProps)(),{lpCourseData:u}=i,c=u?.image||'<div className="course-img"><img src="https://placehold.co/500x300?text=Course+Image"/></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(s.InspectorControls,null,(0,e.createElement)(l.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.SelectControl,{label:(0,t.__)("Size","learnpress"),value:r.attributes.size,options:[{label:"Thumbnail",value:"thumbnail"},{label:"Medium",value:"medium"},{label:"Large",value:"large"},{label:"Full",value:"full"},{label:"Custom",value:"custom"}],onChange:e=>{"custom"!==e&&n({customWidth:500,customHeight:300}),n({size:e})}}),"custom"===r.attributes.size?(0,e.createElement)("div",{style:{display:"flex",gap:"12px"}},(0,e.createElement)(l.__experimentalInputControl,{label:(0,t.__)("Width","learnpress"),type:"number",min:0,style:{flex:1},placeholder:(0,t.__)("Auto","learnpress"),suffix:(0,e.createElement)("div",{style:{marginRight:"8px"}},(0,t.__)("px","learnpress")),value:r.attributes.customWidth,onChange:e=>{const t=parseInt(e,10);!isNaN(Number(t))&&t>=0?n({customWidth:Number(t)}):n({customWidth:500})}}),(0,e.createElement)(l.__experimentalInputControl,{label:(0,t.__)("Height","learnpress"),type:"number",min:0,style:{flex:1},placeholder:(0,t.__)("Auto","learnpress"),suffix:(0,e.createElement)("div",{style:{marginRight:"8px"}},(0,t.__)("px","learnpress")),value:r.attributes.customHeight,onChange:e=>{const t=parseInt(e,10);!isNaN(Number(t))&&t>=0?n({customHeight:Number(t)}):n({customHeight:300})}})):"",(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Make the image a link","learnpress"),checked:!!r.attributes.isLink,onChange:e=>{r.setAttributes({isLink:!!e})}}),r.attributes.isLink?(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Open is new tab","learnpress"),checked:!!r.attributes.target,onChange:e=>{r.setAttributes({target:!!e})}}):"")),(0,e.createElement)("a",null,(0,e.createElement)("div",{...o,dangerouslySetInnerHTML:{__html:(()=>{if(u?.image&&"custom"===a.size&&500===a.customWidth&&300===a.customHeight)return u?.image;let e=2560,t=2560;return"thumbnail"===a.size&&(e=150,t=150),"medium"===a.size&&(e=300,t=300),"large"===a.size&&(e=1024,t=724),"full"===a.size&&(e=2560,t=2560),"custom"===a.size&&(a.customWidth&&a.customWidth>0&&(e=a.customWidth),a.customHeight&&a.customHeight>0&&(t=a.customHeight),0==a.customWidth&&(e=2560),0==a.customHeight&&(e=2560)),`<div class="course-img"><img src="https://placehold.co/${e}x${t}?text=Course+Image" alt="course thumbnail placeholder"</div>`})()||c}})))},a=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-image","title":"Course Image","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Image Course PHP templates.","textdomain":"learnpress","keywords":["image single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"isLink":{"type":"boolean","default":true},"target":{"type":"boolean","default":false},"size":{"type":"string","default":"custom"},"customWidth":{"type":"number","default":500},"customHeight":{"type":"number","default":300}},"supports":{"multiple":true,"html":false,"shadow":true,"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}}}}'),i=window.wp.blocks,o=window.wp.data;let u=null;var c,m,p;c=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],m=n,p=e=>{(0,i.registerBlockType)(e.name,{...e,edit:r,save:a})},(0,o.subscribe)(()=>{const e={...m},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const s=t.getCurrentPostId();null!==s&&u!==s&&(u=s,(0,i.getBlockType)(e.name)&&((0,i.unregisterBlockType)(e.name),c.includes(s)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,i.registerBlockType)(n.name,{...n,edit:r,save:a})})();