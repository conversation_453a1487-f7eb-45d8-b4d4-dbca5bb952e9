!function(e){"use strict";let t,c;const a=function(e){t.toggleClass("loading",void 0===e||e)},s=function(t){c=e("#learn-press-setup-form");const a=c.serializeJSON();return e.extend(a,t||{})},n=function(){const t=e(this).children(":selected").html().match(/\((.*)\)/),c=t?t[1]:"";e("#currency-pos").children().each(function(){const t=e(this);let a=t.html();switch(t.val()){case"left":a=a.replace(/\( (.*)69/,"( "+c+"69");break;case"right":a=a.replace(/9([^0-9]*) \)/,"9"+c+" )");break;case"left_with_space":a=a.replace(/\( (.*) 6/,"( "+c+" 6");break;case"right_with_space":a=a.replace(/9 (.*) \)/,"9 "+c+" )")}t.html(a)})},r=function(){e.post({url:"",dataType:"html",data:s({"lp-ajax":"get-price-format"}),success(t){e("#preview-price").html(t)}})},l=function(c){c.preventDefault(),a(),e.post({url:e(this).attr("href"),dataType:"html",data:s({"lp-ajax":"setup-create-pages"}),success(c){!function(c){const a=e(c);t.replaceWith(a),t=a}(e(c).contents().filter("#main")),e(".learn-press-dropdown-pages").LP("DropdownPages"),a(!1)}})};e(function(){t=e("#main"),c=e("#learn-press-setup-form"),e(".learn-press-select2").select2(),e(document).on("change","#currency",n).on("change","input, select",r).on("click","#create-pages",l)})}(jQuery);