(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.components,window.wp.blockEditor),n=n=>{const r=(0,t.useBlockProps)();return(0,e.createElement)("div",{...r},(0,e.createElement)("div",{className:"learn-press-comments"},(0,e.createElement)("div",{className:"comment-respond"},(0,e.createElement)("h3",{className:"comment-reply-title"},"Leave a Reply"),(0,e.createElement)("p",{class:"comment-form-comment"},(0,e.createElement)("label",{for:"comment"},"Comment ",(0,e.createElement)("span",{class:"required"},"*"))," ",(0,e.createElement)("textarea",{name:"comment",cols:"45",rows:"8",maxlength:"65525"})),(0,e.createElement)("p",{class:"form-submit wp-block-button"},(0,e.createElement)("input",{name:"submit",type:"submit",class:"submit wp-element-button",value:"Post Comment"})))))},r=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-comment","title":"Item Comment","category":"learnpress-category","icon":"admin-comments","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item comment","learnpress"],"usesContext":[],"supports":{"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,l=window.wp.data;let o=null;var m,i,c;m=["learnpress/learnpress//single-lp_course_item"],i=s,c=e=>{(0,a.registerBlockType)(e.name,{...e,edit:n,save:r})},(0,l.subscribe)(()=>{const e={...i},t=(0,l.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&o!==n&&(o=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),m.includes(n)?(e.ancestor=null,c(e)):(e.ancestor||(e.ancestor=[]),c(e))))}),(0,a.registerBlockType)(s.name,{...s,edit:n,save:r})})();