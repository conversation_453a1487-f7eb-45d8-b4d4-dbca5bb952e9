(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,s=s=>{const l=(0,n.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.Inspector<PERSON>s,null,(0,e.createElement)(r.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.Select<PERSON>,{label:(0,t.__)("Display Modes","learnpress"),value:s.attributes.hidden,options:[{label:"Icon + Number + Text",value:""},{label:"Icon + Number",value:"text"},{label:"Number + Text",value:"icon"}],onChange:e=>s.setAttributes({hidden:e||""})}))),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"wrapper-instructor-total-courses"},s.attributes.hidden&&"icon"===s.attributes.hidden?"":(0,e.createElement)("span",{className:"lp-ico lp-icon-courses"}),(0,e.createElement)("span",{className:"instructor-total-courses"},"99"),s.attributes.hidden&&"text"===s.attributes.hidden?"":(0,e.createElement)("span",null," Courses"))))},l=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-course","title":"Instructor Count Course","category":"learnpress-course-elements","icon":"media-text","description":"Renders template instructor count course PHP templates.","textdomain":"learnpress","keywords":["instructor course single","learnpress"],"ancestor":["learnpress/single-instructor"],"attributes":{"hidden":{"type":"string","default":""}},"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;const u=[Number(lpDataAdmin?.single_instructor_id)];var p,d,m;p=u,d=a,m=e=>{(0,o.registerBlockType)(e.name,{...e,edit:s,save:l})},(0,i.subscribe)(()=>{const e={...d},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),p.includes(n)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,o.registerBlockType)(a.name,{...a,edit:s,save:l})})();