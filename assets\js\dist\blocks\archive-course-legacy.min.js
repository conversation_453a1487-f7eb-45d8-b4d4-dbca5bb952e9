(()=>{"use strict";const e=window.React,r=window.wp.i18n,s=window.wp.components,t=window.wp.blockEditor,n=n=>{const o=(0,t.useBlockProps)();return(0,e.createElement)("div",{...o},(0,e.createElement)(s.Placeholder,{label:(0,r.__)("Archive Course (Legacy)","learnpress")},(0,e.createElement)("div",null,(0,r.__)("The block will display the full content of the course archive page. Elements on it cannot be modified!","learnpress"))))},o=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/archive-course-legacy","title":"Archive Course (Legacy)","category":"learnpress-legacy","description":"Renders template Archive Course PHP templates.","textdomain":"learnpress","ancestor":[],"keywords":["legacy","learnpress"],"usesContext":[],"supports":{"align":true}}'),a=window.wp.blocks,c=window.wp.data;let i=null;var p,u,d;p=["learnpress/learnpress//archive-lp_course","learnpress/learnpress//taxonomy-course_category","learnpress/learnpress//archive-course_tag"],u=l,d=e=>{(0,a.registerBlockType)(e.name,{...e,edit:n,save:o})},(0,c.subscribe)(()=>{const e={...u},r=(0,c.select)("core/editor")||null;if(!r||"function"!=typeof r.getCurrentPostId||!r.getCurrentPostId())return;const s=r.getCurrentPostId();null!==s&&i!==s&&(i=s,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(s)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))}),(0,a.registerBlockType)(l.name,{...l,edit:n,save:o})})();