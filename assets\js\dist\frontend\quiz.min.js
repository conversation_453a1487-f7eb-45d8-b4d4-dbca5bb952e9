(()=>{var e={1537:()=>{LP.Hook.addAction("lp-compatible-builder",()=>{LP.Hook.removeAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend&&[...document.querySelectorAll("#popup-content")][0].addEventListener("scroll",()=>{window.dispatchEvent(new Event("resize"))})}),LP.Hook.addAction("lp-quiz-compatible-builder",()=>(LP.Hook.removeAction("lp-quiz-compatible-builder"),LP.Hook.doAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend?window.elementorFrontend.init():"undefined"!=typeof vc_js?("undefined"!=typeof vc_round_charts&&vc_round_charts(),"undefined"!=typeof vc_pieChart&&vc_pieChart(),"undefined"!=typeof vc_line_charts&&vc_line_charts(),window.vc_js()):void 0)),LP.Hook.addAction("lp-question-compatible-builder",()=>(LP.Hook.removeAction("lp-question-compatible-builder"),LP.Hook.removeAction("lp-quiz-compatible-builder"),LP.Hook.doAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend?window.elementorFrontend.init():"undefined"!=typeof vc_js?("undefined"!=typeof vc_round_charts&&vc_round_charts(),"undefined"!=typeof vc_pieChart&&vc_pieChart(),"undefined"!=typeof vc_line_charts&&vc_line_charts(),window.vc_js()):void 0))},2747:e=>{"use strict";function t(e,s){var n;if(Array.isArray(s))for(n=0;n<s.length;n++)t(e,s[n]);else for(n in s)e[n]=(e[n]||[]).concat(s[n])}e.exports=function(e){var s,n={};return t(n,e),(s=function(e){return function(t){return function(s){var r,i,a=n[s.type],o=t(s);if(a)for(r=0;r<a.length;r++)(i=a[r](s,e))&&e.dispatch(i);return o}}}).effects=n,s}},6942:(e,t)=>{var s;!function(){"use strict";var n={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var s=arguments[t];s&&(e=a(e,i(s)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var s in e)n.call(e,s)&&e[s]&&(t=a(t,s));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(s=function(){return r}.apply(t,[]))||(e.exports=s)}()}},t={};function s(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";s.r(n),s.d(n,{default:()=>We,init:()=>Ze});var e={};s.r(e),s.d(e,{__requestBeforeStartQuiz:()=>ue,__requestCheckAnswerSuccess:()=>fe,__requestShowHintSuccess:()=>_e,__requestStartQuizSuccess:()=>le,__requestSubmitQuiz:()=>de,__requestSubmitQuizSuccess:()=>pe,checkAnswer:()=>we,markQuestionRendered:()=>Se,sendKey:()=>ye,setCurrentPage:()=>oe,setCurrentQuestion:()=>ae,setQuizData:()=>ie,setQuizMode:()=>Ee,showHint:()=>qe,startQuiz:()=>ce,submitQuiz:()=>me,updateUserQuestionAnswers:()=>he,updateUserQuestionFibAnswers:()=>ge});var t={};s.r(t),s.d(t,{getCurrentQuestion:()=>Oe,getData:()=>Ce,getDefaultRestArgs:()=>Ae,getItemStatus:()=>ke,getProp:()=>Qe,getQuestion:()=>Le,getQuestionAnswered:()=>Te,getQuestionMark:()=>Re,getQuestionOptions:()=>Pe,getQuestions:()=>Ie,getQuestionsSelectedAnswers:()=>Me,getQuizAnswered:()=>Ne,getQuizAttempts:()=>be,getUserMark:()=>Ue,isCheckedAnswer:()=>xe,isCorrect:()=>De});const r=window.React,i=window.wp.element,a=window.wp.compose,o=window.wp.data,u=window.wp.i18n,l=e=>{let t;const s=86400;if(e>s)t=(e-e%s)/s,e%=s;else if(e==s)return"24:00";const n=new Date(1e3*e).toUTCString().match(/\d{2}:\d{2}:\d{2}/)[0].split(":");return t&&(n[0]=parseInt(n[0])+24*t),n.join(":")},{Hook:c}=LP,d=()=>{const e=e=>(0,o.select)("learnpress/quiz").getData(e),t=c.applyFilters("quiz-meta-fields",{duration:{title:(0,u.__)("Duration:","learnpress"),name:"duration",content:l(e("duration"))||"--"},passingGrade:{title:(0,u.__)("Passing grade:","learnpress"),name:"passing-grade",content:e("passingGrade")||"--"},questionsCount:{title:(0,u.__)("Questions:","learnpress"),name:"questions-count",content:e("questionIds")?e("questionIds").length:0}});return t&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("ul",{className:"quiz-intro"},Object.values(t).map((e,t)=>{const s=e.name||t;return(0,r.createElement)("li",{key:`quiz-intro-field-${t}`,className:`quiz-intro-item quiz-intro-item--${s}`},(0,r.createElement)("div",{className:"quiz-intro-item__title",dangerouslySetInnerHTML:{__html:e.title}}),(0,r.createElement)("span",{className:"quiz-intro-item__content",dangerouslySetInnerHTML:{__html:e.content}}))})))};class p extends i.Component{startQuiz=e=>{e&&e.preventDefault();const t=document.querySelector(".lp-button.start");t&&t.setAttribute("disabled","disabled"),t.classList.add("loading");const{startQuiz:s,status:n}=this.props;if("completed"===n){const{confirm:e,isOpen:s}=(0,o.select)("learnpress/modal");if("no"===e((0,u.__)("Are you sure you want to retake the quiz?","learnpress"),this.startQuiz))return!s()&&t&&t.removeAttribute("disabled"),void t.classList.remove("loading")}s()};nav=e=>t=>{let{questionNav:s,currentPage:n,numPages:r,setCurrentPage:i}=this.props;"prev"===e?n>1?n-=1:n="infinity"===s?r:1:n<r?n+=1:n="infinity"===s?1:r,i(n)};moveTo=e=>t=>{t.preventDefault();const{numPages:s,setCurrentPage:n}=this.props;e<1||e>s||n(e)};isLast=()=>{const{currentPage:e,numPages:t}=this.props;return e===t};isFirst=()=>{const{currentPage:e}=this.props;return 1===e};submit=()=>{const{submitQuiz:e}=this.props,{confirm:t}=(0,o.select)("learnpress/modal");"no"!==t((0,u.__)("Are you sure to submit the quiz?","learnpress"),this.submit)&&e()};setQuizMode=e=>()=>{const{setQuizMode:t}=this.props;t(e)};isReviewing=()=>{const{isReviewing:e}=this.props;return e};pageNumbers(e){const{numPages:t,currentPage:s}=this.props;if(t<2)return"";(e={numPages:t,currentPage:s,midSize:1,endSize:1,prevNext:!0,...e||{}}).endSize<1&&(e.endSize=1),e.midSize<0&&(e.midSize=1);const n=[...Array(t).keys()];let i=!1;return(0,r.createElement)("div",{className:"nav-links"},e.prevNext&&!this.isFirst()&&(0,r.createElement)("button",{className:"page-numbers prev","data-type":"question-navx",onClick:this.nav("prev")},(0,u.__)("Prev","learnpress")),n.map(t=>(t+=1)===e.currentPage?(i=!0,(0,r.createElement)("span",{key:`page-number-${t}`,className:"page-numbers current"},t)):t<=e.endSize||e.currentPage&&t>=e.currentPage-e.midSize&&t<=e.currentPage+e.midSize||t>e.numPages-e.endSize?(i=!0,(0,r.createElement)("button",{key:`page-number-${t}`,className:"page-numbers",onClick:this.moveTo(t)},t)):i?(i=!1,(0,r.createElement)("span",{key:`page-number-${t}`,className:"page-numbers dots"},"…")):""),e.prevNext&&!this.isLast()&&(0,r.createElement)("button",{className:"page-numbers next","data-type":"question-navx",onClick:this.nav("next")},(0,u.__)("Next","learnpress")))}render(){const{status:e,questionNav:t,isReviewing:s,showReview:n,numPages:i,question:a,questionsPerPage:o,canRetry:l,retakeNumber:c,requiredPassword:d,allowRetake:p}=this.props,m=["quiz-buttons"];("started"===e||s)&&m.push("align-center"),"questionNav"===t&&m.push("infinity"),this.isFirst()&&m.push("is-first"),this.isLast()&&m.push("is-last");const h=document.querySelector("#popup-sidebar"),g=document.querySelector("#learn-press-quiz-app");let _="";_="started"===e||s?{marginLeft:h&&h.offsetWidth/2,width:g&&g.offsetWidth}:null;let q=" fixed";return"no"==lpQuizSettings.navigationPosition&&(q=" nav-center"),(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:m.join(" ")},(0,r.createElement)("div",{className:"button-left"+("started"===e||s?q:""),style:_},("completed"===e&&l||-1!==["","viewed"].indexOf(e))&&!s&&!d&&(0,r.createElement)("button",{className:"lp-button start",onClick:this.startQuiz},"completed"===e?`${(0,u.__)("Retake","learnpress")} ${p?"":" "+(c?` (${c})`:"")} `:" "+(0,u.__)("Start","learnpress")),("started"===e||s)&&i>1&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"questions-pagination"},this.pageNumbers()))),(0,r.createElement)("div",{className:"button-right"},"started"===e&&(0,r.createElement)(r.Fragment,null,("infinity"===t||this.isLast())&&!s&&(0,r.createElement)("button",{className:"lp-button submit-quiz",onClick:this.submit},(0,u.__)("Finish Quiz","learnpress"))),s&&n&&(0,r.createElement)("button",{className:"lp-button back-quiz",onClick:this.setQuizMode("")},(0,u.__)("Result","learnpress")),"completed"===e&&n&&!s&&(0,r.createElement)("button",{className:"lp-button review-quiz",onClick:this.setQuizMode("reviewing")},(0,u.__)("Review","learnpress")))),this.props.message&&!0!==this.props.success&&(0,r.createElement)("div",{className:"learn-press-message error"},this.props.message))}}const m=(0,a.compose)((0,o.withSelect)(e=>{const{getData:t}=e("learnpress/quiz");return{status:t("status"),showCheck:t("instantCheck"),checkedQuestions:t("checkedQuestions"),hintedQuestions:t("hintedQuestions"),questionsPerPage:t("questionsPerPage")}}))(e=>{const{showCheck:t,checkedQuestions:s,hintedQuestions:n,question:i,status:a,type:o,Button:u}=e;if("started"!==a)return!1;const l=(0,r.createElement)(u,{question:i});switch(o){case"hint":return n?!!i.hasHint&&-1===n.indexOf(i.id)&&l:l;case"check":return!!t&&(s?-1===s.indexOf(i.id)&&l:l)}}),h=(0,a.compose)([(0,o.withSelect)(e=>{const{getData:t,getCurrentQuestion:s}=e("learnpress/quiz"),n={id:t("id"),status:t("status"),questionIds:t("questionIds"),questionNav:t("questionNav"),isReviewing:t("reviewQuestions")&&"reviewing"===t("mode"),showReview:t("reviewQuestions"),showCheck:t("instantCheck"),checkedQuestions:t("checkedQuestions"),hintedQuestions:t("hintedQuestions"),numPages:t("numPages"),pages:t("pages"),currentPage:t("currentPage"),questionsPerPage:t("questionsPerPage"),pageNumbers:t("pageNumbers"),keyPressed:t("keyPressed"),canRetry:t("retakeCount")>0&&t("retaken")<t("retakeCount"),retakeNumber:t("retakeCount")>0&&t("retaken")<t("retakeCount")?t("retakeCount")-t("retaken"):null,message:t("messageResponse")||!1,success:void 0===t("successResponse")||t("successResponse"),requiredPassword:t("requiredPassword"),allowRetake:t("allowRetake")};if(1===n.questionsPerPage&&(n.question=s("object")),1===lpQuizSettings.checkNorequizenroll){const e=window.localStorage.getItem("quiz_off_retaken_"+lpQuizSettings.id);t("retakeCount")>e?(n.retakeNumber=t("retakeCount")-e,n.canRetry=!0):n.canRetry=!1}return n.allowRetake&&(n.canRetry=!0),n}),(0,o.withDispatch)((e,{id:t})=>{const{startQuiz:s,setCurrentQuestion:n,submitQuiz:r,setQuizMode:i,showHint:a,checkAnswer:o,setCurrentPage:u}=e("learnpress/quiz");return{startQuiz:s,setCurrentQuestion:n,setQuizMode:i,setCurrentPage:u,submitQuiz(e){r(e)},showHint(e){a(e)},checkAnswer(e){o(e)}}})])(p);class g extends i.Component{showHint=()=>{const{showHint:e,question:t}=this.props;e(t.id,!t.showHint)};render(){const{question:e}=this.props;return e.hint?(0,r.createElement)("button",{className:"btn-show-hint",onClick:this.showHint},(0,r.createElement)("span",null,(0,u.__)("Hint","learnpress"))):""}}const _=(0,a.compose)((0,o.withDispatch)((e,{id:t})=>{const{showHint:s}=e("learnpress/quiz");return{showHint(e,t){s(e,t)}}}))(g);var q=s(6942),f=s.n(q);class w extends i.Component{constructor(){super(...arguments),this.state={loading:!1}}checkAnswer=()=>{const{checkAnswer:e,question:t,answered:s}=this.props;"fill_in_blanks"===t.type&&document.querySelector(`.question-fill_in_blanks[data-id="${t.id}"]`).querySelectorAll(".lp-fib-input > input").forEach(s=>{if(s.value.length>0)return this.setState({loading:!0}),e(t.id),!1}),s&&(e(t.id),this.setState({loading:!0}))};render(){const{answered:e}=this.props;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{className:f()("lp-button","instant-check",{loading:this.state.loading}),onClick:this.checkAnswer},(0,r.createElement)("span",{className:"instant-check__icon"}),(0,u.__)("Check answers","learnpress"),!e&&(0,r.createElement)("div",{className:"instant-check__info",dangerouslySetInnerHTML:{__html:(0,u.__)("You need to answer the question before checking the answer key.","learnpress")}})))}}const S=(0,a.compose)((0,o.withSelect)((e,{question:{id:t}})=>{const{getQuestionAnswered:s}=e("learnpress/quiz");return{answered:s(t)}}),(0,o.withDispatch)((e,{id:t})=>{const{checkAnswer:s}=e("learnpress/quiz");return{checkAnswer(e){s(e)}}}))(w),E=function(e){const{question:t}=e,s={"instant-check":()=>(0,r.createElement)(m,{type:"check",Button:S,question:t}),hint:()=>(0,r.createElement)(m,{type:"hint",Button:_,question:t})};return(0,r.createElement)(i.Fragment,null,LP.config.questionFooterButtons().map(e=>(0,r.createElement)(i.Fragment,{key:`button-${e}`},s[e]&&s[e]())))},y=window.jQuery,{uniqueId:v,isArray:z,isNumber:P,bind:k}=lodash;class Q extends i.Component{constructor(){super(...arguments),this.state={time:null,showHint:!1},this.$wrap=null}componentDidMount(e){const{question:t,isCurrent:s,markQuestionRendered:n}=this.props;return s&&n(t.id),this.state.time||this.setState({time:new Date}),LP.Hook.doAction("lp-question-compatible-builder"),"undefined"!=typeof MathJax&&void 0!==MathJax.Hub&&MathJax.Hub.Queue(["Typeset",MathJax.Hub]),e}setRef=e=>{this.$wrap=y(e)};parseOptions=e=>(e&&(e=z(e)?e:JSON.parse(CryptoJS.AES.decrypt(e.data,e.key,{format:CryptoJSAesJson}).toString(CryptoJS.enc.Utf8)),e=z(e)?e:JSON.parse(e)),e||[]);getWrapperClass=()=>{const{question:e,answered:t}=this.props,s=["question","question-"+e.type],n=this.parseOptions(e.options);return n.length&&void 0!==n[0].isTrue&&s.push("question-answered"),s};getEditLink=()=>{const{question:e,editPermalink:t}=this.props;return t?t.replace(/post=(.*[0-9])/,`post=${e.id}`):""};editPermalink=e=>(0,u.sprintf)('<a href="%s">%s</a>',e,(0,u.__)("Edit","learnpress"));render(){const{question:e,isShow:t,isShowIndex:s,isShowHint:n,status:a}=this.props,o=LP.questionTypes.default,l=this.getEditLink();l&&jQuery("#wp-admin-bar-edit-lp_question").find(".ab-item").attr("href",l);const c={index:()=>s?(0,r.createElement)("span",{className:"question-index"},s,"."):"",title:()=>(0,r.createElement)("span",{dangerouslySetInnerHTML:{__html:e.title}}),hint:()=>(0,r.createElement)(_,{question:e}),"edit-permalink":()=>l&&(0,r.createElement)("span",{dangerouslySetInnerHTML:{__html:this.editPermalink(l)},className:"edit-link"})},d={title:()=>(0,r.createElement)("h4",{className:"question-title"},LP.config.questionTitleParts().map(e=>(0,r.createElement)(i.Fragment,{key:`title-part-${e}`},c[e]&&c[e]()))),content:()=>(0,r.createElement)("div",{className:"question-content",dangerouslySetInnerHTML:{__html:e.content}}),"answer-options":()=>this.$wrap&&(0,r.createElement)(o,{...this.props,$wrap:this.$wrap}),explanation:()=>e.explanation&&(0,r.createElement)(i.Fragment,null,(0,r.createElement)("div",{className:"question-explanation-content"},(0,r.createElement)("strong",{className:"explanation-title"},(0,u.__)("Explanation","learnpress"),":"),(0,r.createElement)("div",{dangerouslySetInnerHTML:{__html:e.explanation}}))),hint:()=>e.hint&&!e.explanation&&e.showHint&&(0,r.createElement)(i.Fragment,null,(0,r.createElement)("div",{className:"question-hint-content"},(0,r.createElement)("strong",{className:"hint-title"},(0,u.__)("Hint","learnpress"),":"),(0,r.createElement)("div",{dangerouslySetInnerHTML:{__html:e.hint}}))),buttons:()=>"started"===a&&(0,r.createElement)(E,{question:e})},p=LP.config.questionBlocks();return(0,r.createElement)(i.Fragment,null,(0,r.createElement)("div",{className:this.getWrapperClass().join(" "),style:{display:t?"":"none"},"data-id":e.id,ref:this.setRef},p.map(e=>(0,r.createElement)(i.Fragment,{key:`block-${e}`},d[e]?d[e]():""))))}}const b=(0,a.compose)([(0,o.withSelect)((e,{question:{id:t}})=>{const{getData:s,getQuestionAnswered:n,getQuestionMark:r}=e("learnpress/quiz");return{status:s("status"),questions:s("question"),answered:n(t),questionsRendered:s("questionsRendered"),editPermalink:s("editPermalink"),numPages:s("numPages"),mark:r(t)||""}}),(0,o.withDispatch)(e=>{const{updateUserQuestionAnswers:t,updateUserQuestionFibAnswers:s,markQuestionRendered:n}=e("learnpress/quiz");return{markQuestionRendered:n,updateUserQuestionAnswers:t,updateUserQuestionFibAnswers:s}})])(Q);class N extends i.Component{constructor(e){super(...arguments),this.needToTop=!1,this.state={isReviewing:null,currentPage:0,self:this}}static getDerivedStateFromProps(e,t){const s=["isReviewing","currentPage"],n={};for(let r=0;r<s.length;r++)e[s[r]]!==t[s[r]]&&(n[s[r]]=e[s[r]]);return Object.values(n).length?(t.self.needToTop=!0,n):null}componentDidUpdate(){this.needToTop&&(jQuery("#popup-content").animate({scrollTop:0}).find(".content-item-scrollable:last").animate({scrollTop:0}),this.needToTop=!1)}startQuiz=e=>{e.preventDefault();const{startQuiz:t}=this.props;t()};isInVisibleRange=(e,t)=>{const{currentPage:s,questionsPerPage:n}=this.props;return s===Math.ceil(t/n)};nav=e=>{const{sendKey:t}=this.props;switch(e.keyCode){case 37:return t("left");case 38:case 40:return;case 39:return t("right");default:e.keyCode>=49&&e.keyCode<=57&&t(e.keyCode-48)}};render(){const{status:e,currentQuestion:t,questions:s,questionsRendered:n,isReviewing:i,questionsPerPage:a}=this.props;let o=!0;return"completed"!==e||i||(o=!1),(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{tabIndex:100,onKeyUp:this.nav},(0,r.createElement)("div",{className:"quiz-questions",style:{display:o?"":"none"}},s.map((e,s)=>{const i=!a&&t===e.id,o=n&&-1!==n.indexOf(e.id),u=this.isInVisibleRange(e.id,s+1);return o||!o||u?(0,r.createElement)(b,{key:`loop-question-${e.id}`,isCurrent:i,isShow:u,isShowIndex:!!a&&s+1,questionsPerPage:a,question:e}):""}))))}}const I=(0,a.compose)((0,o.withSelect)((e,t,s)=>{const{getData:n,getQuestions:r}=e("learnpress/quiz");return{status:n("status"),currentQuestion:n("currentQuestion"),questions:r(),questionsRendered:n("questionsRendered"),isReviewing:"reviewing"===n("mode"),numPages:n("numPages"),currentPage:n("currentPage"),questionsPerPage:n("questionsPerPage")||1}}),(0,o.withDispatch)(e=>{const{startQuiz:t,sendKey:s}=e("learnpress/quiz");return{startQuiz:t,sendKey:s}}))(N),C=()=>{const e=(0,o.select)("learnpress/quiz").getData("attempts")||[],t=e&&!!e.length;return!!t&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"quiz-attempts"},(0,r.createElement)("h4",{className:"attempts-heading"},(0,u.__)("Last Attempt","learnpress")),t&&(0,r.createElement)("table",null,(0,r.createElement)("thead",null,(0,r.createElement)("tr",null,(0,r.createElement)("th",{className:"quiz-attempts__questions"},(0,u.__)("Questions","learnpress")),(0,r.createElement)("th",{className:"quiz-attempts__spend"},(0,u.__)("Time spent","learnpress")),(0,r.createElement)("th",{className:"quiz-attempts__marks"},(0,u.__)("Marks","learnpress")),(0,r.createElement)("th",{className:"quiz-attempts__grade"},(0,u.__)("Passing grade","learnpress")),(0,r.createElement)("th",{className:"quiz-attempts__result"},(0,u.__)("Result","learnpress")))),(0,r.createElement)("tbody",null,e.map((e,t)=>(0,r.createElement)("tr",{key:`attempt-${t}`},(0,r.createElement)("td",{className:"quiz-attempts__questions"},`${e.questionCorrect} / ${e.questionCount}`),(0,r.createElement)("td",{className:"quiz-attempts__spend"},e.timeSpend||"--"),(0,r.createElement)("td",{className:"quiz-attempts__marks"},`${e.userMark} / ${e.mark}`),(0,r.createElement)("td",{className:"quiz-attempts__grade"},e.passingGrade||"-"),(0,r.createElement)("td",{className:"quiz-attempts__result"},`${parseFloat(e.result).toFixed(2)}%`," ",(0,r.createElement)("span",null,e.graduationText))))))))},A=()=>{const{getData:e}=(0,o.select)("learnpress/quiz"),{submitQuiz:t}=(0,o.dispatch)("learnpress/quiz"),s=e("totalTime"),n=e("duration"),[a,u]=(0,i.useState)(s);let[l,c]=(0,i.useState)(0);return(0,i.useEffect)(()=>{const e=setInterval(()=>{if(n>0){let s=a;s-=1,s=wp.hooks.applyFilters("js-lp-quiz-remaining_time",s,n),s>0?(u(s),l++,c(n-s)):(clearInterval(e),t())}else l++,c(l),u(l)},1e3);return()=>clearInterval(e)},[a,l]),(0,r.createElement)("div",{className:"countdown"},(0,r.createElement)("i",{className:"lp-icon-stopwatch"}),(0,r.createElement)("span",null,((e=":")=>{const t=[];let n;return s<3600?(t.push((a-a%60)/60),t.push(a%60)):s&&(t.push((a-a%3600)/3600),n=a%3600,t.push((n-n%60)/60),t.push(n%60)),t.map(e=>e<10?`0${e}`:e).join(e)})()),(0,r.createElement)("input",{type:"hidden",name:"lp-quiz-time-spend",value:l}))},T=window.wp.url,R=jQuery;let O=null;const L={elLPOverlay:null,elMainContent:null,elTitle:null,elBtnYes:null,elBtnNo:null,elFooter:null,elCalledModal:null,callBackYes:null,instance:null,init(){return!!this.instance||(this.elLPOverlay=R(".lp-overlay"),!!this.elLPOverlay.length&&(O=this.elLPOverlay,this.elMainContent=O.find(".main-content"),this.elTitle=O.find(".modal-title"),this.elBtnYes=O.find(".btn-yes"),this.elBtnNo=O.find(".btn-no"),this.elFooter=O.find(".lp-modal-footer"),R(document).on("click",".close, .btn-no",function(){O.hide()}),R(document).on("click",".btn-yes",function(e){e.preventDefault(),e.stopPropagation(),"function"==typeof L.callBackYes&&L.callBackYes(e)}),this.instance=this,!0))},setElCalledModal(e){this.elCalledModal=e},setContentModal(e,t){this.elMainContent.html(e),"function"==typeof t&&t()},setTitleModal(e){this.elTitle.html(e)}},x=L,D={elBtnFinishCourse:null,elBtnCompleteItem:null,init(){x.init()&&void 0!==lpGlobalSettings&&"yes"===lpGlobalSettings.option_enable_popup_confirm_finish&&(this.elBtnFinishCourse=document.querySelectorAll(".lp-btn-finish-course"),this.elBtnCompleteItem=document.querySelector(".lp-btn-complete-item"),this.elBtnCompleteItem&&this.elBtnCompleteItem.addEventListener("click",e=>{e.preventDefault();const t=e.target.closest("form");x.elLPOverlay.show(),x.setTitleModal(t.dataset.title);const s=document.createElement("div");s.appendChild(document.createTextNode(t.dataset.confirm));const n=s.innerHTML;x.setContentModal('<div class="pd-2em">'+n+"</div>"),x.callBackYes=()=>{t.submit()}}),this.elBtnFinishCourse&&this.elBtnFinishCourse.forEach(e=>e.addEventListener("click",e=>{e.preventDefault();const t=e.target.closest("form");x.elLPOverlay.show(),x.setTitleModal(t.dataset.title),x.setContentModal('<div class="pd-2em">'+t.dataset.confirm+"</div>"),x.callBackYes=()=>{t.submit()}})))}},{debounce:M}=lodash,U=()=>{const[e,t]=(0,i.useState)(0),[s,n]=(0,i.useState)(!1),a=(0,o.useSelect)(e=>e("learnpress/quiz").getData("id"),[]),l=(0,o.useSelect)(e=>e("learnpress/quiz").getData("results"),[]),c=(0,o.useSelect)(e=>e("learnpress/quiz").getData("passingGrade"),[]),d=(0,o.useSelect)(e=>e("learnpress/quiz").getData("submitting"),[]);(0,i.useEffect)(()=>{m();let e="";if(e=l.graduation?l.graduation:l.result>=q?"passed":"failed",e){const t=document.querySelector(`.course-curriculum .course-item.course-item-${a}`);t&&(t.classList.remove("failed","passed"),t.classList.add("has-status","status-completed",e))}const t=[...document.querySelectorAll("#popup-header .items-progress")][0];if(t){const e=t.dataset.totalItems,s=t.querySelector(".items-completed"),n=t.querySelector(".learn-press-progress__active");if(s){const t=document.querySelectorAll("#popup-sidebar .course-curriculum .course-item__status .completed");s.textContent=parseInt(t.length);const r=100-100*parseInt(t.length)/parseInt(e);n.style.left="-"+r+"%"}}},[l]),(0,i.useEffect)(()=>{void 0!==d&&p()},[d]);const p=()=>{const e=document.querySelectorAll(".popup-header__inner");e.length>0&&0===e[0].querySelectorAll("form.form-button-finish-course").length&&(async e=>{const t=await wp.apiFetch({path:(0,T.addQueryArgs)("lp/v1/lazy-load/items-progress",{courseId:lpGlobalSettings.post_id||"",userId:lpGlobalSettings.user_id||""}),method:"GET"}),{data:s}=t;e.innerHTML+=s,e.classList.add("can-finish-course"),D.init()})(e[0])},m=()=>{t(0),n(!1),jQuery.easing._customEasing=function(e,t,s,n,r){return n*Math.sqrt(1-(t=t/r-1)*t)+s},M(()=>{const e=jQuery("<span />").css({width:1,height:1}).appendTo(document.body);e.css("left",0).animate({left:l.result},{duration:1500,step:(e,s)=>{t(e)},done:()=>{n(!0),e.remove(),jQuery("#quizResultGrade").css({transform:"scale(1.3)",transition:"all 0.25s"}),M(()=>{jQuery("#quizResultGrade").css({transform:"scale(1)"})},500)()},easing:"_customEasing"})},l.result>0?1e3:10)()};let h=e;Number.isInteger(e)||(h=parseFloat(e).toFixed(2));const g=190*Math.PI,_={strokeDasharray:`${g} ${g}`,strokeDashoffset:g-h/100*g},q=parseFloat(l.passingGrade||c);let f="";f=l.graduation?l.graduation:h>=q?"passed":"failed";let w="";w=l.graduationText?l.graduationText:"passed"===f?(0,u.__)("Passed","learnpress"):(0,u.__)("Failed","learnpress");const S=["quiz-result",f];return(0,r.createElement)("div",{className:S.join(" ")},(0,r.createElement)("h3",{className:"result-heading"},(0,u.__)("Your Result","learnpress")),(0,r.createElement)("div",{id:"quizResultGrade",className:"result-grade"},(0,r.createElement)("svg",{className:"circle-progress-bar",width:200,height:200},(0,r.createElement)("circle",{className:"circle-progress-bar__circle",stroke:"",strokeWidth:10,style:_,fill:"transparent",r:95,cx:100,cy:100})),(0,r.createElement)("span",{className:"result-achieved"},`${h}%`),(0,r.createElement)("span",{className:"result-require"},q+"%"||0)),s&&(0,r.createElement)("p",{className:"result-message"},w),(0,r.createElement)("ul",{className:"result-statistic"},(0,r.createElement)("li",{className:"result-statistic-field result-time-spend"},(0,r.createElement)("span",null,(0,u.__)("Time spent","learnpress")),(0,r.createElement)("p",null,l.timeSpend)),(0,r.createElement)("li",{className:"result-statistic-field result-point"},(0,r.createElement)("span",null,(0,u.__)("Points","learnpress")),(0,r.createElement)("p",null,l.userMark," / ",l.mark)),(0,r.createElement)("li",{className:"result-statistic-field result-questions"},(0,r.createElement)("span",null,(0,u.__)("Questions","learnpress")),(0,r.createElement)("p",null,l.questionCount)),(0,r.createElement)("li",{className:"result-statistic-field result-questions-correct"},(0,r.createElement)("span",null,(0,u.__)("Correct","learnpress")),(0,r.createElement)("p",null,l.questionCorrect)),(0,r.createElement)("li",{className:"result-statistic-field result-questions-wrong"},(0,r.createElement)("span",null,(0,u.__)("Wrong","learnpress")),(0,r.createElement)("p",null,l.questionWrong)),(0,r.createElement)("li",{className:"result-statistic-field result-questions-skipped"},(0,r.createElement)("span",null,(0,u.__)("Skipped","learnpress")),(0,r.createElement)("p",null,l.questionEmpty)),void 0!==l.minusPoint&&(0,r.createElement)("li",{className:"result-statistic-field result-questions-minus"},(0,r.createElement)("span",null,(0,u.__)("Minus points","learnpress")),(0,r.createElement)("p",null,l.minusPoint))))},F=jQuery,{debounce:H}=lodash,$=()=>{const{submitQuiz:e}=(0,o.dispatch)("learnpress/quiz");(0,i.useEffect)(()=>{const e=F("#popup-content");if(!e.length)return;const t=e.find(".content-item-scrollable:eq(1)"),s=e.find(".quiz-status").offset().top-92;let n=!1;t.on("scroll",()=>{if(t.scrollTop()>=s){if(n)return;n=!0}else{if(!n)return;n=!1}n?e.addClass("fixed-quiz-status"):e.removeClass("fixed-quiz-status")})},[]);const t=()=>{const{confirm:s}=(0,o.select)("learnpress/modal");"no"!==s((0,u.__)("Are you sure to submit the quiz?","learnpress"),t)&&e()},{getData:s,getUserMark:n}=(0,o.select)("learnpress/quiz"),a=s("currentPage"),l=s("questionsPerPage"),c=s("numberQuestionsToDo"),d=s("submitting"),p=(s("duration"),n()),m=["quiz-status"],h=(a-1)*l+1;let g=h+l-1,_="";return g=Math.min(g,c),d&&m.push("submitting"),_=g<c?l>1?(0,u.sprintf)((0,u.__)("Question <span>%d to %d of %d</span>","learnpress"),h,g,c):(0,u.sprintf)((0,u.__)("Question <span>%d of %d</span>","learnpress"),h,c):(0,u.sprintf)((0,u.__)("Question <span>%d of %d</span>","learnpress"),h,g),(0,r.createElement)("div",{className:m.join(" ")},(0,r.createElement)("div",null,(0,r.createElement)("div",{className:"questions-index",dangerouslySetInnerHTML:{__html:_}}),(0,r.createElement)("div",{className:"current-point"},(0,u.sprintf)((0,u.__)("Earned Point: %s","learnpress"),p)),(0,r.createElement)("div",null,(0,r.createElement)("div",{className:"submit-quiz"},(0,r.createElement)("button",{className:"lp-button",id:"button-submit-quiz",onClick:t},d?(0,u.__)("Submitting…","learnpress"):(0,u.__)("Finish Quiz","learnpress"))),(0,r.createElement)(A,null))))},{omit:j,flow:B,isArray:J,chunk:G}=lodash,{camelCaseDashObjectKeys:K}=LP,{get:W,set:Z}=LP.localStorage,Y={},V=(e,t)=>(t.currentPage&&Z(`Q${e.id}.currentPage`,t.currentPage),{...e,...t}),X=B(o.combineReducers,e=>(t,s)=>e(t,s),e=>(t,s)=>e(t,s),e=>(t,s)=>e(t,s))({a:(e={a:1},t)=>e,b:(e={b:2},t)=>e}),ee=(0,o.combineReducers)({blocks:X,userQuiz:(e=Y,t)=>{switch(t.type){case"SET_QUIZ_DATA":1>t.data.questionsPerPage&&(t.data.questionsPerPage=1);const s=G(e.questionIds||t.data.questionIds,t.data.questionsPerPage);return t.data.numPages=s.length,t.data.pages=s,{...e,...t.data,currentPage:W(`Q${t.data.id}.currentPage`)||t.data.currentPage};case"SUBMIT_QUIZ":return{...e,submitting:!0};case"START_QUIZ":case"START_QUIZ_SUCCESS":return((e,t)=>{const s=void 0!==t.results.success&&t.results.success,n=t.results.message||!1,r=G(t.results.results.questionIds,e.questionsPerPage);return e.numPages=r.length,V(e,{checkedQuestions:[],hintedQuestions:[],mode:"",currentPage:1,...t.results.results,successResponse:s,messageResponse:n})})(e,t);case"SET_CURRENT_QUESTION":return Z(`Q${e.id}.currentQuestion`,t.questionId),{...e,currentQuestion:t.questionId};case"SET_CURRENT_PAGE":return Z(`Q${e.id}.currentPage`,t.currentPage),{...e,currentPage:t.currentPage};case"SUBMIT_QUIZ_SUCCESS":return((e,t)=>{localStorage.removeItem(`LP_Quiz_${e.id}_Answered`);const s=e.questions.map(s=>{const n={};return e.reviewQuestions&&(t.results.questions[s.id]?.explanation&&(n.explanation=t.results.questions[s.id].explanation),t.results.questions[s.id]?.options&&(n.options=t.results.questions[s.id].options)),{...s,...n}});return V(e,{submitting:!1,currentPage:1,...t.results,questions:[...s]})})(e,t);case"UPDATE_USER_QUESTION_ANSWERS":return"started"===e.status?((e,t)=>{const{answered:s,id:n}=e,r={...s[t.questionId]||{},answered:t.answers,temp:!0};return n&&localStorage.setItem(`LP_Quiz_${n}_Answered`,JSON.stringify({...e.answered,[t.questionId]:r})),{...e,answered:{...e.answered,[t.questionId]:r}}})(e,t):e;case"UPDATE_USER_QUESTION_FIB_ANSWERS":return"started"===e.status?((e,t)=>{const{id:s}=e,{questionId:n,idInput:r,valueInput:i}=t;return e.answered.hasOwnProperty(n)?e.answered[n].answered[r]=i:e.answered[t.questionId]={answered:{[r]:i},temp:!0},localStorage.setItem(`LP_Quiz_${s}_Answered`,JSON.stringify(e.answered)),e})(e,t):e;case"MARK_QUESTION_RENDERED":return((e,t)=>{const{questionsRendered:s}=e;return J(s)?(s.push(t.questionId),{...e,questionsRendered:[...s]}):{...e,questionsRendered:[t.questionId]}})(e,t);case"SET_QUIZ_MODE":return"reviewing"==t.mode?V(e,{mode:t.mode}):{...e,mode:t.mode};case"SET_QUESTION_HINT":return((e,t)=>{const s=e.questions.map(e=>e.id==t.questionId?{...e,showHint:t.showHint}:e);return{...e,questions:[...s]}})(e,t);case"CHECK_ANSWER":return((e,t)=>{const s=e.questions.map(e=>{if(e.id!==t.questionId)return e;const s={explanation:t.explanation};return t.options&&(s.options=t.options),{...e,...s}}),n={...e.answered,[t.questionId]:t.result};let r=localStorage.getItem(`LP_Quiz_${e.id}_Answered`);return r&&(r={...JSON.parse(r),...n},localStorage.setItem(`LP_Quiz_${e.id}_Answered`,JSON.stringify(r))),{...e,questions:[...s],answered:n,checkedQuestions:[...e.checkedQuestions,t.questionId]}})(e,t);case"SEND_KEY":return{...e,keyPressed:t.keyPressed}}return e}}),te=window.LP.dataControls;function se(){const e=[].slice.call(arguments,2);(0,o.dispatch)(arguments[0])[arguments[1]](...e)}const{camelCaseDashObjectKeys:ne,Hook:re}=LP;function ie(e,t){return{type:"SET_QUIZ_DATA",data:ne(t="string"==typeof e?{[e]:t}:e)}}function ae(e){return{type:"SET_CURRENT_QUESTION",questionId:e}}function oe(e){return{type:"SET_CURRENT_PAGE",currentPage:e}}function ue(e,t,s){return{type:"BEFORE_START_QUIZ"}}function le(e,t,s,n){return re.doAction("quiz-started",e,t,s,n),{type:"START_QUIZ_SUCCESS",quizId:t,courseId:s,userId:n,results:e}}const ce=function*(){const{itemId:e,courseId:t}=(0,o.select)("learnpress/quiz").getDefaultRestArgs();if(!0!==re.applyFilters("before-start-quiz",!0,e,t))return;let s=yield(0,te.apiFetch)({path:"lp/v1/users/start-quiz",method:"POST",data:{item_id:e,course_id:t}});const n=document.querySelector(".lp-button.start");if("error"!==s.status){s=re.applyFilters("request-start-quiz-response",s,e,t);const{results:n}=s,{duration:r,status:i,question_ids:a,questions:o}=n;if(1===lpQuizSettings.checkNorequizenroll){const e="quiz_off_"+lpQuizSettings.id;window.localStorage.removeItem(e);const t={endTime:Date.now()+1e3*r,status:i,question_ids:a,questions:o};window.localStorage.setItem(e,JSON.stringify(t));const s="quiz_off_retaken_"+lpQuizSettings.id;let n=window.localStorage.getItem(s);null===n?n=0:n++,window.localStorage.setItem(s,n)}window.localStorage.removeItem("LP"),window.location.reload()}else{const e=document.querySelector(".quiz-buttons"),t=`<div class="learn-press-message error">${s.message}</div>`;e.insertAdjacentHTML("afterend",t),n.classList.remove("loading")}};function de(){return{type:"SUBMIT_QUIZ"}}function pe(e,t,s){return re.doAction("quiz-submitted",e,t,s),{type:"SUBMIT_QUIZ_SUCCESS",results:e}}function*me(){const{getDefaultRestArgs:e,getQuestionsSelectedAnswers:t}=(0,o.select)("learnpress/quiz"),{itemId:s,courseId:n}=e();if(!0!==re.applyFilters("before-submit-quiz",!0))return;const r=t();if(1===lpQuizSettings.checkNorequizenroll){const e=`quiz_off_${lpQuizSettings.id}`,t=window.localStorage.getItem(e),n=JSON.parse(t),i=`LP_Quiz_${s}_Answered`,a=localStorage.getItem(i);if(null!==a){const e=JSON.parse(a);for(const[t,s]of Object.entries(e))r[t]=s.answered}n.question_ids.forEach(e=>{r[e]||(r[e]="")})}let i=0;const a=document.querySelector("input[name=lp-quiz-time-spend]");a&&(i=a.value);let u=yield(0,te.apiFetch)({path:"lp/v1/users/submit-quiz",method:"POST",data:{item_id:s,course_id:n,answered:r,time_spend:i}});if(u=re.applyFilters("request-submit-quiz-response",u,s,n),"success"===u.status){if(1===lpQuizSettings.checkNorequizenroll){const e="quiz_off_"+lpQuizSettings.id,t=window.localStorage.getItem(e);if(null!==t){const s=JSON.parse(t);s.status=u.results.status,s.results=u.results.results,window.localStorage.setItem(e,JSON.stringify(s)),window.localStorage.removeItem("LP_Quiz_"+lpQuizSettings.id+"_Answered")}}yield se("learnpress/quiz","__requestSubmitQuizSuccess",ne(u.results),s,n)}}function he(e,t,s,n=0,r=0){return{type:"UPDATE_USER_QUESTION_ANSWERS",questionId:e,answers:t}}function ge(e,t,s,n,r=0,i=0){return{type:"UPDATE_USER_QUESTION_FIB_ANSWERS",questionId:e,idInput:t,valueInput:s}}function _e(e,t){return{type:"SET_QUESTION_HINT",questionId:e,showHint:t}}function*qe(e,t){yield se("learnpress/quiz","__requestShowHintSuccess",e,t)}function fe(e,t){return{type:"CHECK_ANSWER",questionId:e,...t}}function*we(e){const{getDefaultRestArgs:t,getQuestionAnswered:s}=(0,o.select)("learnpress/quiz"),{itemId:n,courseId:r}=t(),i=yield(0,te.apiFetch)({path:"lp/v1/users/check-answer",method:"POST",data:{item_id:n,course_id:r,question_id:e,answered:s(e)||""}});if("success"===i.status){if(1===lpQuizSettings.checkNorequizenroll){const t="quiz_off_"+lpQuizSettings.id,s=window.localStorage.getItem(t);if(null!==s){const n=JSON.parse(s),r=i.options;void 0===n.checked_questions?(n.checked_questions=[],n.checked_questions.push(e)):-1===n.checked_questions.indexOf(e)&&n.checked_questions.push(e),void 0===n.question_options?(n.question_options={},n.question_options[e]=r):void 0===n.question_options[e]&&(n.question_options[e]=r),window.localStorage.setItem(t,JSON.stringify(n))}}yield se("learnpress/quiz","__requestCheckAnswerSuccess",e,ne(i))}}function Se(e){return{type:"MARK_QUESTION_RENDERED",questionId:e}}function Ee(e){return{type:"SET_QUIZ_MODE",mode:e}}function ye(e){return setTimeout(()=>{se("learnpress/quiz","sendKey","")},300),{type:"SEND_KEY",keyPressed:e}}const{get:ve,isArray:ze}=lodash,Pe=function(e,t){console.time("parseOptions");let s=Le(e,t).options;return s=ze(s)?s:JSON.parse(CryptoJS.AES.decrypt(s.data,s.key,{format:CryptoJSAesJson}).toString(CryptoJS.enc.Utf8)),s=ze(s)?s:JSON.parse(s),console.timeEnd("parseOptions"),s};function ke(e,t){const s=(0,o.select)("course-learner/user").getItemById(t);return s?ve(s,"userSettings.status"):""}function Qe(e,t,s){return e[t]||s}function be(e,t){const s=(0,o.select)("course-learner/user").getItemById(t);return s?ve(s,"userSettings.attempts"):[]}function Ne(e,t){const s=(0,o.select)("course-learner/user").getItemById(t);return s?ve(s,"userSettings.answered",{}):{}}function Ie(e){const{userQuiz:t}=e,s=ve(t,"questions");return s?Object.values(s):[]}function Ce(e,t){const{userQuiz:s}=e;return t?ve(s,t):s}function Ae(e){const{userQuiz:t}=e;return{itemId:t.id,courseId:t.courseId}}function Te(e,t){const{userQuiz:s}=e;return ve(s,`answered.${t}.answered`)||void 0}function Re(e,t){const{userQuiz:s}=e;return ve(s,`answered.${t}.mark`)||void 0}function Oe(e,t=""){if((ve(e,"userQuiz.questionsPerPage")||1)>1)return!1;const s=ve(e,"userQuiz.currentPage")||1;return ve(e,"object"===t?`userQuiz.questions[${s-1}]`:`userQuiz.questionIds[${s-1}]`)}const Le=function(e,t){const{userQuiz:s}=e;return(0,o.select)("learnpress/quiz").getQuestions().find(e=>e.id==t)};function xe(e,t){return-1!==(ve(e,"userQuiz.checkedQuestions")||[]).indexOf(t)}function De(e,t){}const Me=function(e,t){const s=ve(e,"userQuiz.answered"),n={};for(const e in s)if(s.hasOwnProperty(e)&&(s[e].temp||s[e].blanks)){if(t&&e===t)return s[e].answered;n[e]=s[e].answered}return n};function Ue(e){const t=e.userQuiz||{},{answered:s,negativeMarking:n,questions:r,checkedQuestions:i}=t;let a=0;for(let e in s){if(!s.hasOwnProperty(e))continue;e=parseInt(e);const t=s[e],o=t.questionMark?t.questionMark:function(){const t=r.find(t=>t.id===e);return t?t.point:0}();i.indexOf(e),t.temp||(n?t.answered&&(a=t.correct?a+t.mark:a-o):t.answered&&t.correct&&(a+=t.mark))}return a>0?a:0}var Fe=s(2747),He=s.n(Fe);const $e={ENROLL_COURSE_X:(e,t)=>{}},{controls:je}=LP.dataControls;!function(e){let t=()=>{throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")};const s={getState:e.getState,dispatch:(...e)=>t(...e)};t=He()($e)(s)(e.dispatch),e.dispatch=t}((0,o.registerStore)("learnpress/quiz",{reducer:ee,selectors:t,actions:e,controls:{...je}}));const{chunk:Be}=lodash;class Je extends i.Component{constructor(e){super(...arguments),this.state={currentPage:1,numPages:0,pages:[]}}componentDidMount(){const{settings:e,setQuizData:t}=this.props,{question_ids:s,questions_per_page:n}=e,r=Be(s,n);e.currentPage=1,e.numPages=r.length,e.pages=r;const i=!!e.id&&localStorage.getItem(`LP_Quiz_${e.id}_Answered`);i&&(e.answered=JSON.parse(i)),t(e)}componentDidUpdate(e,t,s){const{status:n}=e,r=document.querySelector(".quiz-content");void 0!==n&&r&&(r.style.display="none")}startQuiz=e=>{this.props.startQuiz()};render(){const{status:e,isReviewing:t,answered:s}=this.props;wp.hooks.doAction("lp-js-quiz-answer",s,e);const n=-1!==["","completed","viewed"].indexOf(e)||!e,i=-1!==["","viewed",void 0].indexOf(e)||!e;return void 0!==e&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",null,!t&&"completed"===e&&(0,r.createElement)(U,null),!t&&i&&(0,r.createElement)(d,null),"started"===e&&(0,r.createElement)($,null),(-1!==["completed","started"].indexOf(e)||t)&&(0,r.createElement)(I,null),(0,r.createElement)(h,null),n&&!t&&(0,r.createElement)(C,null)))}}const Ge=(0,a.compose)([(0,o.withSelect)(e=>{const{getQuestions:t,getData:s}=e("learnpress/quiz");return{questions:t(),status:s("status"),store:s(),answered:s("answered"),isReviewing:"reviewing"===s("mode"),questionIds:s("questionIds"),checkCount:s("instantCheck"),questionsPerPage:s("questionsPerPage")||1}}),(0,o.withDispatch)(e=>{const{setQuizData:t,startQuiz:s}=e("learnpress/quiz");return{setQuizData:t,startQuiz:s}})])(Je);s(1537);const{modal:{default:Ke}}=LP,We=Ge,Ze=(e,t)=>{if(1===lpQuizSettings.checkNorequizenroll){const e="quiz_off_"+lpQuizSettings.id,s=window.localStorage.getItem(e);if(null!==s){const e=JSON.parse(s);if(t.status=e.status,t.questions=e.questions,"started"===e.status){const s=Date.now();t.total_time=Math.floor((e.endTime-s)/1e3)}else"completed"===e.status&&(t.results=e.results,t.answered=e.results.answered,t.questions=e.results.questions);if(void 0!==e.checked_questions&&(t.checked_questions=e.checked_questions),void 0!==e.question_options)for(const s in t.questions){const n=t.questions[s];void 0!==e.question_options[n.id]&&(n.options=e.question_options[n.id]),t.questions[s]=n}}}wp.element.render((0,r.createElement)(Ke,null,(0,r.createElement)(Ge,{settings:t})),[...document.querySelectorAll(e)][0]),LP.Hook.doAction("lp-quiz-compatible-builder")}})(),(window.LP=window.LP||{}).quiz=n})();