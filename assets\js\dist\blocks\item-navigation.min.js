(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.components,window.wp.blockEditor),n=n=>{const a=(0,t.useBlockProps)();return(0,e.createElement)("div",{...a},(0,e.createElement)("div",{class:"course-item-nav","data-nav":"all"},(0,e.createElement)("div",{class:"prev"},(0,e.createElement)("div",{class:"course-item-nav__name"},"Lesson 1"),(0,e.createElement)("a",null,"Prev")),(0,e.createElement)("div",{class:"next"},(0,e.createElement)("div",{class:"course-item-nav__name"},"Lesson 3"),(0,e.createElement)("a",null,"Next"))))},a=e=>null,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-navigation","title":"Item Nagivation","category":"learnpress-category","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item nagivation course","learnpress"],"usesContext":[],"supports":{"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),s=window.wp.blocks,l=window.wp.data;let i=null;const o=window.wp.primitives,c=window.ReactJSXRuntime,m=(0,c.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(o.Path,{d:"M4 13.5h6v-3H4v3zm8.2-2.5.8-.3V14h1V9.3l-******* 1zm7.1-1.2c-.5-.6-1.2-.5-1.7-.4-.3.1-.5.2-.7.3l.1 1.1c.2-.2.5-.4.8-.5.3-.1.6 0 .******* 0 .8-.2 1.1-.5.8-.9 1.6-1.4 2.5h2.7v-1h-.9c.3-.6.8-1.4.9-2.1 0-.3-.1-.8-.3-1.1z"})});var p,u,d;p=["learnpress/learnpress//single-lp_course_item"],u=r,d=e=>{(0,s.registerBlockType)(e.name,{...e,icon:m,edit:n,save:a})},(0,l.subscribe)(()=>{const e={...u},t=(0,l.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&i!==n&&(i=n,(0,s.getBlockType)(e.name)&&((0,s.unregisterBlockType)(e.name),p.includes(n)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))}),(0,s.registerBlockType)(r.name,{...r,icon:m,edit:n,save:a})})();