(()=>{"use strict";const e=window.React,t=window.wp.blockEditor,r=r=>{const n=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"instructor-description"},(0,e.createElement)("p",null,"It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout."))))},n=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-description","title":"Instructor Description","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Description PHP templates.","textdomain":"learnpress","keywords":["instructor description single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),l=window.wp.blocks,a=window.wp.data;let o=null;const i=[Number(lpDataAdmin?.single_instructor_id)];var c,p,u;c=i,p=s,u=e=>{(0,l.registerBlockType)(e.name,{...e,edit:r,save:n})},(0,a.subscribe)(()=>{const e={...p},t=(0,a.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,u(e)):(e.ancestor||(e.ancestor=[]),u(e))))}),(0,l.registerBlockType)(s.name,{...s,edit:r,save:n})})();