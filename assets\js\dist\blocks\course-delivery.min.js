(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,l=(window.wp.element,l=>{const s=(0,n.useBlockProps)(),{attributes:o,setAttributes:a,context:i}=l,{lpCourseData:c}=i;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.Inspector<PERSON>ontrols,null,(0,e.createElement)(r.<PERSON><PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.<PERSON>,{label:(0,t.__)("Show Label","learnpress"),checked:o.showLabel,onChange:e=>{a({showLabel:e})}}),(0,e.createElement)(r.<PERSON><PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:o.showIcon,onChange:e=>{a({showIcon:e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},o.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-bookmark-o"></i>'}}),o.showLabel?(0,t.__)("Delivery type:","learnpress"):""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:'<span class="course-deliver-type">Private 1-1</span>'}}))))}),s=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-delivery","title":"Course Delivery Type","category":"learnpress-course-elements","description":"Show type delivery of course.","textdomain":"learnpress","keywords":["delivery","type","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,i=window.wp.data;let c=null;const p=window.wp.primitives,u=window.ReactJSXRuntime,m=(0,u.jsx)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,u.jsx)(p.Path,{d:"M6 5V18.5911L12 13.8473L18 18.5911V5H6Z"})});var w,d,g;w=["learnpress/learnpress//single-lp_course-offline"],d=o,g=e=>{(0,a.registerBlockType)(e.name,{...e,icon:m,edit:l,save:s})},(0,i.subscribe)(()=>{const e={...d},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),w.includes(n)?(e.ancestor=null,g(e)):(e.ancestor||(e.ancestor=[]),g(e))))}),(0,a.registerBlockType)(o.name,{...o,icon:m,edit:l,save:s})})();