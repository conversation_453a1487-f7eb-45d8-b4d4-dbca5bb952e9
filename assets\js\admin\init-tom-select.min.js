import{AdminUtilsFunctions,Api,Utils}from"./utils-admin.js";const handleResponse=(e,t,n,a,s={},i)=>{if(!(e&&t&&n&&a&&i))return;const l=e=>{if(!n.keyGetValue?.text||!n.keyGetValue.key_render)return;let t=n.keyGetValue.text;for(const[a,s]of Object.entries(n.keyGetValue.key_render))t=t.replace(new RegExp(`{{${s}}}`,"g"),e[s]);return t},r=t.dataset?.saved?JSON.parse(t.dataset.saved):0;let o=[];e.data[n.dataType].length>0&&(o=e.data[n.dataType].map(e=>({value:e[n.keyGetValue.value],text:l(e)})));const c={items:r,render:{item:(e,t)=>`<li data-id="${e.value}">\n\t\t\t\t\t\t<div class="item">${e.text}</div>\n\t\t\t\t\t</li>`},onChange:e=>{e.length<1&&(t.value="")},...s,options:o};return null!=t.tomSelectInstance?(t.tomSelectInstance.addOptions(o),o):(t.tomSelectInstance=AdminUtilsFunctions.buildTomSelect(t,c,a,{},i),o)},initTomSelectWithOption=(e,t={})=>e?null!=e.tomSelectInstance?null:void(e.tomSelectInstance=AdminUtilsFunctions.buildTomSelect(e,t)):null,initTomSelect=(e,t={},n={})=>{if(!e)return;if(e.classList.contains("loaded"))return;e.classList.add("loaded");const a=e.dataset?.saved?JSON.parse(e.dataset.saved):0,s=e?.dataset?.struct?JSON.parse(e.dataset.struct):"";if(!s)return void initTomSelectWithOption(e);const i=(e,t)=>{const n=t.parentElement;return n.tagName.toLowerCase()===e?n:"html"!==n.tagName.toLowerCase()&&i(e,n)},l=i("form",e);if(l){const t=l.querySelector('input[name="'+e.getAttribute("name")+'"]');t&&t.remove()}const r=s.dataSendApi??"",o=s.urlApi??"",c={...s.setting,...t};if(!o)return void initTomSelectWithOption(e,c);const d=(e="",t,n)=>{const s=o,i={current_ids:a,...r,...t};i.search=e;const l={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(i)};Utils.lpFetchAPI(s,l,n)},u={success:t=>{handleResponse(t,e,s,d,c,u)}};let m=[];"object"==typeof a&&(m=Object.entries(a).map(([e,t])=>({key:e,value:t}))),r?.id_not_in&&(m=[...m,...r.id_not_in]),n.id_not_in=m.join(","),d("",n,u)},searchUserOnListPost=()=>{if("0"===lpDataAdmin.show_search_author_field)return;const e=document.querySelector("#posts-filter");if(!e)return;let t=e.querySelector(".search-box");if(t||(e.insertAdjacentHTML("afterbegin",lpDataAdmin.show_search_author_field),t=e.querySelector(".search-box")),!t)return;if(t.querySelector("select#author"))return;(()=>{let n="";const a=lpDataAdmin.urlParams.author;a&&(n=JSON.stringify(a));const s={urlApi:Api.admin.apiSearchUsers,dataType:"users",keyGetValue:{value:"ID",text:"{{display_name}}(#{{ID}}) - {{user_email}}",key_render:{display_name:"display_name",user_email:"user_email",ID:"ID"}},setting:{placeholder:"Choose user"}},i=`<select data-struct='${JSON.stringify(s)}' style='display:none;' data-saved='${n}'\n\t\t\t\t\tid="author" name="author" class="select lp-tom-select"></select>`,l=t.querySelector('input[name="s"]');l&&l.insertAdjacentHTML("afterend",i);const r=e.querySelector('input[name="author"]');r&&r.remove()})()},initElsTomSelect=()=>{const e=document.querySelectorAll("select.lp-tom-select:not(.loaded)");e.length&&e.forEach(e=>{e.closest(".widget-liquid-left")||initTomSelect(e)})};export{initTomSelect,searchUserOnListPost,initElsTomSelect};