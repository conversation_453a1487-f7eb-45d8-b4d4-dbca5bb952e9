(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,s=s=>{const l=(0,n.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.Inspector<PERSON>s,null,(0,e.createElement)(r.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.Select<PERSON>ontrol,{label:(0,t.__)("Display Modes","learnpress"),value:s.attributes.hidden,options:[{label:"Icon + Number + Text",value:""},{label:"Icon + Number",value:"text"},{label:"Number + Text",value:"icon"}],onChange:e=>s.setAttributes({hidden:e||""})}))),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"wrapper-instructor-total-students"},s.attributes.hidden&&"icon"===s.attributes.hidden?"":(0,e.createElement)("span",{className:"lp-ico lp-icon-students"}),(0,e.createElement)("span",{className:"instructor-total-students"},"99"),s.attributes.hidden&&"text"===s.attributes.hidden?"":(0,e.createElement)("span",null," Students"))))},l=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-student","title":"Instructor Count Student","category":"learnpress-course-elements","icon":"admin-users","description":"Renders template instructor count student PHP templates.","textdomain":"learnpress","keywords":["instructor student single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"attributes":{"hidden":{"type":"string","default":""}},"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),o=window.wp.blocks,i=window.wp.data;let u=null;const c=[Number(lpDataAdmin?.single_instructor_id)];var d,p,m;d=c,p=a,m=e=>{(0,o.registerBlockType)(e.name,{...e,edit:s,save:l})},(0,i.subscribe)(()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&u!==n&&(u=n,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),d.includes(n)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,o.registerBlockType)(a.name,{...a,edit:s,save:l})})();