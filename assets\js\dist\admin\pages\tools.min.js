(()=>{"use strict";const e=jQuery;let t=null;const s={elLPOverlay:null,elMainContent:null,elTitle:null,elBtnYes:null,elBtnNo:null,elFooter:null,elCalledModal:null,callBackYes:null,instance:null,init(){return!!this.instance||(this.elLPOverlay=e(".lp-overlay"),!!this.elLPOverlay.length&&(t=this.elLPOverlay,this.elMainContent=t.find(".main-content"),this.elTitle=t.find(".modal-title"),this.elBtnYes=t.find(".btn-yes"),this.elBtnNo=t.find(".btn-no"),this.elFooter=t.find(".lp-modal-footer"),e(document).on("click",".close, .btn-no",function(){t.hide()}),e(document).on("click",".btn-yes",function(e){e.preventDefault(),e.stopPropagation(),"function"==typeof s.callBackYes&&s.callBackYes(e)}),this.instance=this,!0))},setElCalledModal(e){this.elCalledModal=e},setContentModal(e,t){this.elMainContent.html(e),"function"==typeof t&&t()},setTitleModal(e){this.elTitle.html(e)}},l=s,n=function(e,t,s){wp.apiFetch({path:e,method:"POST",data:t}).then(e=>{"function"==typeof s.success&&s.success(e)}).catch(e=>{"function"==typeof s.error&&s.error(e)}).then(()=>{"function"==typeof s.completed&&s.completed()})},a=jQuery,o=a("#lp-tool-upgrade-db"),r=()=>{let e=0;const t=o.find(".wrapper-terms-upgrade"),s=o.find(".wrapper-lp-status-upgrade"),r=o.find(".wrapper-lp-upgrade-message");let i=null;const c=a("input[name=message-when-upgrading]").val();if(t.length){l.setContentModal(t.html());const e=l.elLPOverlay.find(".terms-upgrade"),a=e.find("input[name=lp-agree-term]"),o=e.find(".error");i=function(){return o.hide(),o.removeClass("learn-press-message"),a.is(":checked")?(n("/lp/v1/database/agree_terms",{agree_terms:1},{}),l.elFooter.find(".learn-press-notice").remove(),l.setContentModal(s.html()),!0):(o.show(),o.addClass("learn-press-message"),l.elMainContent.animate({scrollTop:o.offset().top}),!1)}}else l.setContentModal(s.html()),i=function(){return!0};l.setTitleModal(o.find("h2").html()),l.elBtnYes.text("Upgrade"),l.elBtnYes.show(),l.elBtnNo.text("Cancel"),l.callBackYes=function(t){if(!i())return;"Upgrade"===t.target.innerText&&l.elFooter.prepend('<span class="learn-press-notice">'+c+"</span>"),e=1,l.elBtnYes.hide(),l.elBtnNo.hide();const s="/lp/v1/database/upgrade",d=l.elLPOverlay.find(".lp-group-step"),p=o.find(".lp-item-step"),u=[];a.each(p,function(e,t){const s=a(t);if(!s.hasClass("completed")){const e=s.find("input").val();u.push(e)}});const m={steps:u,step:u[0]};let h=null;const g=(e,t)=>{h=d.find("input[value="+e+"]").closest(".lp-item-step"),h.addClass("running"),100===t&&h.removeClass("running").addClass("completed"),h.find(".progress-bar").css("width",t+"%"),h.find(".percent").text(t+"%")};g(u[0],.1);const f={success:t=>{g(m.step,t.percent),m.step!==t.name&&g(t.name,.1),(e=>{if(h=d.find("input[value="+e+"]").closest(".lp-item-step"),!h.length)return;const t=h.offset().top-l.elMainContent.offset().top+l.elMainContent.scrollTop();l.elMainContent.stop().animate({scrollTop:t},600)})(m.step),"success"===t.status?(m.step=t.name,m.data=t.data,setTimeout(()=>{n(s,m,f)},800)):"finished"===t.status?(e=0,h.removeClass("running").addClass("completed"),setTimeout(()=>{l.setContentModal(r.html())},1e3),l.elFooter.find(".learn-press-notice").remove(),l.elBtnNo.text("Close"),l.elBtnNo.show(),l.elBtnNo.on("click",()=>{window.location.reload()})):(e=0,l.elFooter.find(".learn-press-notice").remove(),h.removeClass("running").addClass("error"),l.setContentModal(r.html(),function(){l.elBtnYes.text("Retry").show(),l.callBackYes=()=>{window.location.href=lpGlobalSettings.siteurl+"/wp-admin/admin.php?page=learn-press-tools&tab=database&action=upgrade-db"},l.elBtnNo.show(),t.message||(t.message="Upgrade not success! Please clear cache, restart sever then retry or contact to LP to help"),l.elMainContent.find(".learn-press-message").addClass("error").html(t.message)}))},error:t=>{e=0,l.setContentModal(r.html(),function(){l.elBtnYes.text("Retry").show(),l.callBackYes=()=>{window.location.location="wp-admin/admin.php?page=learn-press-tools&tab=database&action=upgrade-db"},l.elBtnNo.show(),t.message||(t.message="Upgrade not success! Something wrong. Please clear cache, restart sever then retry or contact to LP to help"),l.elMainContent.find(".learn-press-message").addClass("error").html(t.message)})},completed:()=>{}};n(s,m,f)},window.onbeforeunload=function(){if(e)return"LP is upgrading Database. Are you want to reload page?"},window.onclose=function(){if(e)return"LP is upgrading Database. Are you want to close page?"}},i=window.React,{__}=wp.i18n,{TextControl:c,Button:d,Spinner:p,CheckboxControl:u,Notice:m}=wp.components,{useState:h,useEffect:g}=wp.element,{addQueryArgs:f}=wp.url,y=()=>{const[e,t]=h(!1),[s,l]=h(""),[n,a]=h([]),[o,r]=h([]),[y,v]=h([]),[b,C]=h(!1);g(()=>{w(s)},[s]);const w=async s=>{try{if(!s||e)return v([]),void a([]);if(s.length<3)return v([{status:"error",message:"Please enter at least 3 characters to searching course."}]),void a([]);t(!0);const l=await wp.apiFetch({path:f("lp/v1/admin/tools/reset-data/search-courses",{s}),method:"GET"}),{status:n,data:o}=l;t(!1),"success"===n?(a(o),v([])):(v([{status:"error",message:l.message||"LearnPress: Search Course Fail!"}]),a([]))}catch(e){console.log(e.message)}};return(0,i.createElement)(i.Fragment,null,(0,i.createElement)("h2",null,__("Reset Course Progress","learnpress")),(0,i.createElement)("div",{className:"description"},(0,i.createElement)("p",null,__("This action will reset course progress of all users who have enrolled.","learnpress")),(0,i.createElement)("p",null,__("Search results only show if courses have user data.","learnpress")),(0,i.createElement)("div",null,(0,i.createElement)(c,{placeholder:__("Search course by name","learnpress"),value:s,onChange:e=>l(e),style:{width:300}}))),e&&(0,i.createElement)(p,null),n.length>0&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)("div",{className:"lp-reset-course_progress",style:{border:"1px solid #eee"}},(0,i.createElement)("div",null,(0,i.createElement)("div",{style:{background:"#eee"}},(0,i.createElement)("div",null,(0,i.createElement)(u,{checked:o.length===n.length,onChange:()=>{o.length===n.length?r([]):r(n.map(e=>e.id))},style:{margin:0}})),(0,i.createElement)("div",null,__("ID","learnpress")),(0,i.createElement)("div",null,__("Name","learnpress")),(0,i.createElement)("div",null,__("Students","learnpress")))),(0,i.createElement)("div",{style:{height:"100%",maxHeight:200,overflow:"auto"}},n.map(e=>(0,i.createElement)("div",{style:{borderTop:"1px solid #eee"},key:e.id},(0,i.createElement)("div",null,(0,i.createElement)(u,{checked:o.includes(e.id),onChange:()=>function(e){const t=[...o];if(t.includes(e)){const s=t.indexOf(e);s>-1&&t.splice(s,1)}else t.push(e);r(t)}(e.id)})),(0,i.createElement)("div",null,"#",e.id),(0,i.createElement)("div",null,e.title),(0,i.createElement)("div",null,e.students))))),b?(0,i.createElement)(p,null):(0,i.createElement)(d,{isPrimary:!0,onClick:()=>(async()=>{if(0===o.length)return void v([{status:"error",message:"Please chooce Course for reset data!"}]);if(!confirm("Are you sure to reset course progress of all users enrolled this course?"))return;const e=[];try{C(!0);for(const t of o){const s=await wp.apiFetch({path:f("lp/v1/admin/tools/reset-data/reset-courses",{courseId:t}),method:"GET"}),{status:l,data:n,message:a}=s;e.push({status:l,message:a||`Course #${t} reset successfully!`})}C(!1)}catch(t){e.push({status:"error",message:t.message||"LearnPress Error: Reset Course Data."})}v(e)})(),style:{marginTop:10,height:30}},__("Reset now","learnpress"))),y.length>0&&y.map((e,t)=>(0,i.createElement)(m,{status:e.status,key:t,isDismissible:!1},e.message)),(0,i.createElement)("style",null,"\t\t\t\t.lp-reset-course_progress .components-base-control__field {\t\t\t\t\tmargin: 0;\t\t\t\t}\t\t\t\t.components-notice{\t\t\t\t\tmargin-left: 0;\t\t\t\t}\t\t\t\t.lp-reset-course_progress > div > div{\t\t\t\t\tdisplay: grid;\t\t\t\t\tgrid-template-columns: 80px 50px 1fr 80px;\t\t\t\t\talign-items: center;\t\t\t\t}\t\t\t\t.lp-reset-course_progress > div > div > div{\t\t\t\t\tmaegin: 0;\t\t\t\t\tpadding: 8px 10px;\t\t\t\t}\t\t\t\t"))};!function(e){const t=e(document);let s=!1;const c=function(t){t.preventDefault();const l=e(this);s||confirm(lpGlobalSettings.i18n.confirm_install_sample_data)&&(l.addClass("disabled").html(l.data("installing-text")),e(".lp-install-sample__response").remove(),s=!0,e.ajax({method:"POST",url:l.attr("href"),data:e(".lp-install-sample__options").serializeJSON(),success(t){l.removeClass("disabled").html(l.data("text")),s=!1,e(t).insertBefore(l.parent())},error(){l.removeClass("disabled").html(l.data("text")),s=!1,e(response).insertBefore(l.parent())}}))},d=function(t){t.preventDefault();const l=e(this);s||confirm(lpGlobalSettings.i18n.confirm_uninstall_sample_data)&&(l.addClass("disabled").html(l.data("uninstalling-text")),s=!0,e.ajax({url:l.attr("href"),success(t){l.removeClass("disabled").html(l.data("text")),s=!1,e(t).insertBefore(l.parent())},error(){l.removeClass("disabled").html(l.data("text")),s=!1,e(response).insertBefore(l.parent())}}))},p=function(t){t.preventDefault();const s=e(this);s.hasClass("disabled")||(s.addClass("disabled").html(s.data("cleaning-text")),e.ajax({url:s.attr("href"),data:{},success(e){s.removeClass("disabled").html(s.data("text"))},error(){s.removeClass("disabled").html(s.data("text"))}}))},u=function(){e.ajax({url:"admin.php?page=lp-toggle-hard-cache-option",data:{v:this.checked?"yes":"no"},success(e){},error(){}})},m=function(t){t.preventDefault(),e(".lp-install-sample__options").toggleClass("hide-if-js")},h=t=>{const s=e(t.target);let l=s.val();l=l.replace(/[^0-9]/g,""),s.val(l),parseInt(l,10)<s.attr("min")&&s.val(s.attr("min")),parseInt(l,10)>s.attr("max")&&s.val(s.attr("max"))};e(function(){(()=>{if(!o.length)return;if(!l.init())return;const e=a(".wrapper-lp-status-upgrade"),t=window.location.search,s=new URLSearchParams(t).get("action");"upgrade-db"===s&&(l.elLPOverlay.show(),l.setTitleModal(o.find("h2").html()),l.setContentModal(a(".wrapper-lp-loading").html())),n("/lp/v1/database/get_steps",{},{success:t=>{const{steps_completed:n,steps_default:o}=t;if(void 0===n||void 0===o)return console.log("invalid steps_completed and steps_default"),!1;let i="";for(const t in o){const c=o[t],d=c.steps;i='<div class="lp-group-step">',i+="<h3>"+c.label+"</h3>";for(const e in d){const t=d[e];let s="";void 0!==n[e]&&(s="completed"),i+='<div class="lp-item-step '+s+'">',i+='<div class="lp-item-step-left"><input type="hidden" name="lp_steps_upgrade_db[]" value="'+t.name+'"  /></div>',i+='<div class="lp-item-step-right">',i+='<label for=""><strong></strong>'+t.label+"</label>",i+='<div class="description">'+t.description+"</div>",i+='<div class="percent"></div>',i+='<span class="progress-bar"></span>',i+="</div>",i+="</div>"}i+="</div>",e.append(i);const p=a(".lp-btn-upgrade-db");"upgrade-db"===s&&r(),p.on("click",function(){l.elLPOverlay.show(),r()})}},error:e=>{},completed:()=>{}})})(),(()=>{const e=document.querySelector("#lp-tool-create-indexes-tables");e&&e.querySelector(".lp-btn-create-indexes").addEventListener("click",t=>{t.preventDefault();const s=e.querySelector(".wrapper-lp-loading");if(!l.init())return;l.elLPOverlay.show(),l.setContentModal(s.innerHTML),l.setTitleModal(e.querySelector("h2").textContent),l.elBtnYes[0].style.display="inline-block",l.elBtnYes[0].textContent="Run",l.elBtnNo[0].textContent="Close";const a={success:e=>{const{status:t,message:s,data:{tables:a,table:o}}=e,r=document.querySelector(".example-lp-group-step");l.setContentModal(r.innerHTML);const i=l.elLPOverlay[0].querySelector(".lp-group-step "),c=(e,t)=>{const s=i.querySelector("input[value="+e+"]").closest(".lp-item-step");s.classList.add("running"),100===t&&(s.classList.remove("running"),s.classList.add("completed")),s.querySelector(".progress-bar").style.width=t};for(const e in a){const t=l.elLPOverlay[0].querySelector(".lp-item-step").cloneNode(!0),s=t.querySelector("input");t.querySelector("label").textContent=`Table: ${e}`,s.value=e,i.append(t)}l.callBackYes=()=>{const e="/lp/v1/admin/tools/create-indexs",t={tables:a,table:o};l.elBtnNo[0].style.display="none",l.elBtnYes[0].style.display="none",c(o,.1);const s={success:a=>{const{status:o,message:r,data:{table:d,percent:p}}=a;c(t.table,p),void 0!==d&&(t.table!==d&&(c(d,.1),(e=>{const t=i.querySelector("input[value="+e+"]").closest(".lp-item-step").offsetTop-l.elMainContent[0].offsetTop+l.elMainContent[0].scrollTop;l.elMainContent.stop().animate({scrollTop:t},600)})(d)),t.table=d),"success"===o?setTimeout(()=>{n(e,t,s)},2e3):"finished"===o?(console.log("finished"),l.elBtnNo[0].style.display="inline-block",l.elBtnNo[0].textContent="Finish"):console.log(r)},error:e=>{console.log(e)},completed:()=>{}};n(e,t,s)}},error:e=>{},completed:()=>{}};n("/lp/v1/admin/tools/list-tables-indexs",{},a)})})(),(()=>{const e=document.querySelector("#lp-tool-re-upgrade-db");if(!e)return;let t="lp/v1/database/check-db-valid-re-upgrade";n(t,{},{success(s){const{data:{can_re_upgrade:l}}=s;if(!l)return;e.style.display="block";const a=e.querySelector(".lp-btn-re-upgrade-db"),o=e.querySelector(".learn-press-message");a.addEventListener("click",()=>{confirm("Are you want to Re Upgrade!")&&(t="lp/v1/database/del-tb-lp-upgrade-db",n(t,{},{success(e){const{status:t,message:s,data:{url:l}}=e;"success"===t&&void 0!==l&&(window.location.href=l)},error(e){o.classList.add("error"),o.textContent=e.message,o.style.display="block"}}))})},error(e){}})})(),document.querySelectorAll("#learn-press-reset-course-users").length>0&&wp.element.render((0,i.createElement)(y,null),[...document.querySelectorAll("#learn-press-reset-course-users")][0]),(()=>{const e=document.querySelector("#lp-tool-clean-database");e&&e.querySelector(".lp-btn-clean-db").addEventListener("click",function(t){t.preventDefault();const s=document.querySelector("#tools-select__id"),a=s.querySelectorAll("ul li input"),o=Array.prototype.slice.call(a).some(e=>e.checked),r=e.querySelector(".tools-prepare__message");if(0==o)return r.style.display="block",void(r.textContent="You must choose at least one table to take this action");r.style.display="none";const i=e.querySelector(".wrapper-lp-loading");if(!l.init())return;l.elLPOverlay.show(),l.setContentModal(i.innerHTML),l.setTitleModal(e.querySelector("h2").textContent),l.elBtnYes[0].style.display="inline-block",l.elBtnYes[0].textContent="Run",l.elBtnNo[0].textContent="Close";const c=new Array;s.querySelectorAll("ul li input:checked").forEach(e=>{c.push(e.value)});const d=c[0],p=i.querySelector(".progressbar__item").getAttribute("data-total"),u=document.querySelector(".lp-modal-body .main-content").querySelector(".lp-tool__message");if(p<=0)return l.elBtnYes[0].style.display="none",u.textContent="There is no data that need to be repaired in the chosen tables",void(u.style.display="block");l.callBackYes=()=>{if(0==confirm("The modified data is impossible to be restored. Please backup your website before doing this."))return;const e=document.querySelector(".lp-modal-body .main-content"),t=e.querySelector(".lp-tool__message");t.textContent="This action is in processing. Don't close this page",t.style.display="block";const s="/lp/v1/admin/tools/clean-tables",a={tables:d,itemtotal:p};l.elBtnNo[0].style.display="none",l.elBtnYes[0].style.display="none";const o={success:t=>{const{status:r,message:i,data:{processed:c,percent:d}}=t,u=e.querySelector(".progressbar__item"),m=u.querySelector(".progressbar__rows"),h=u.querySelector(".progressbar__percent"),g=u.querySelector(".progressbar__value");if(console.log(r),"success"===r)setTimeout(()=>{n(s,a,o)},2e3),m.textContent=c+" / "+p,h.textContent="( "+d+"% )",g.style.width=d+"%";else if("finished"===r){m.textContent=p+" / "+p,h.textContent="( 100% )";const e=document.querySelector(".lp-modal-body .main-content").querySelector(".lp-tool__message");e.textContent="Process has been completed. Press click the finish button to close this popup",e.style.color="white",e.style.background="green",g.style.width="100%",l.elBtnNo[0].style.display="inline-block",l.elBtnNo[0].textContent="Finish",l.elBtnNo[0].addEventListener("click",function(){location.reload()})}else console.log(i)},error:e=>{console.log(e)},completed:()=>{}};n(s,a,o)}})})(),t.on("click",".lp-install-sample__install",c).on("click",".lp-install-sample__uninstall",d).on("click","#learn-press-clear-cache",p).on("click",'input[name="enable_hard_cache"]',u).on("click",".lp-install-sample__toggle-options",m).on("change",'input[name="section-range[]"]',h).on("change",'input[name="item-range[]"]',h).on("change",'input[name="question-range[]"]',h).on("change",'input[name="answer-range[]"]',h).on("change",'input[name="course-price"]',h)})}(jQuery)})();