(()=>{var e={6942:(e,t)=>{var r;!function(){"use strict";var s={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=o(e,n(r)))}return e}function n(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)s.call(e,r)&&e[r]&&(t=o(t,r));return t}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(r=function(){return a}.apply(t,[]))||(e.exports=r)}()}},t={};function r(s){var a=t[s];if(void 0!==a)return a.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blockEditor;var s=r(6942),a=r.n(s);const n=window.wp.data,o=window.wp.element,l=window.wp.components,i=window.wp.i18n,c={};let u;"undefined"!=typeof lpDataAdmin&&(u=lpDataAdmin.lp_rest_url,c.admin={apiAdminNotice:u+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:u+"lp/v1/orders/statistic",apiAddons:u+"lp/v1/addon/all",apiAddonAction:u+"lp/v1/addon/action-n",apiAddonsPurchase:u+"lp/v1/addon/info-addons-purchase",apiSearchCourses:u+"lp/v1/admin/tools/search-course",apiSearchUsers:u+"lp/v1/admin/tools/search-user",apiAssignUserCourse:u+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:u+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(u=lpData.lp_rest_url,c.frontend={apiWidgets:u+"lp/v1/widgets/api",apiCourses:u+"lp/v1/courses/archive-course",apiAJAX:u+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:u+"lp/v1/profile/cover-image"}),u&&(c.apiCourses=u+"lp/v1/courses/");const p=c,m=[["learnpress/course-image"],["learnpress/course-title"],["learnpress/course-price"]];function d({classList:r}){const s=(0,t.useInnerBlocksProps)({className:a()("wp-block-learnpress-course-item-template")},{template:m});return(0,e.createElement)("li",{className:"course"},(0,e.createElement)("div",{...s}))}const v=(0,o.memo)(function({blocks:r,blockContextId:s,classList:a,isHidden:n,setActiveBlockContextId:o}){const l=(0,t.__experimentalUseBlockPreview)({blocks:r}),i=()=>{o(s)},c={display:n?"none":void 0};return(0,e.createElement)("li",{className:"course",tabIndex:0,role:"button",onClick:i,onKeyPress:i,style:c},(0,e.createElement)("div",{...l,className:"wp-block-learnpress-course-item-template"}))}),g=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-item-template","title":"Course Item Template","category":"","description":"Course Item Template","textdomain":"learnpress","keywords":["course item","learnpress"],"usesContext":["lpCourseQuery"],"supports":{"align":true},"attributes":{"layout":{"type":"string","default":"list"},"columns":{"type":"number","default":3}},"ancestor":["learnpress/list-courses"]}');(0,window.wp.blocks.registerBlockType)("learnpress/course-item-template",{...g,icon:{src:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"}))},edit:({clientId:r,context:s,attributes:c,setAttributes:u})=>{const m=(0,t.useBlockProps)({className:a()("learn-press-courses")}),[g,f]=(0,o.useState)(),[y,b]=(0,o.useState)(),[w,h]=(0,o.useState)([]),[_,C]=(0,o.useState)(0),{columns:E}=c,k=s.lpCourseQuery?.pagination_type||"number";(0,o.useEffect)(()=>{var e;const t=null!==(e=s.lpCourseQuery)&&void 0!==e?e:{};let r,a;return(async()=>{try{C(1),a=new AbortController,r=a.signal;const e=await(async(e,t)=>{const r=p.apiCourses;let s="?return_type=json";e&&(s+=`&${new URLSearchParams(e).toString()}`);const a=await fetch(r+s,{method:"GET",signal:t});if(!a.ok)throw new Error(`HTTP error! Status: ${a.status}`);return await a.json()})(t,r),{data:s}=e,{courses:n,page:o,total:l,total_pages:i}=s;b(e),h(n)}catch(e){"AbortError"!==e.name&&console.error("Failed to fetch courses:",e)}finally{C(0)}})(),()=>{a.abort()}},[s.lpCourseQuery?.order_by,s.lpCourseQuery?.limit]);const{blocks:x}=(0,n.useSelect)(e=>{const{getBlocks:s}=e(t.store);return{blocks:s(r)}},[r]),I=(0,o.useMemo)(()=>w?.map(e=>({lpCourseData:e,courseId:e?.ID})),[w]);if(_)return(0,e.createElement)("ul",{...m},(0,e.createElement)("li",null,(0,i.__)("Courses Fetching…","learnpress")));if(0===w.length&&!_){const e=[{ID:1,post_title:(0,i.__)("Course One","learnpress")},{ID:2,post_title:(0,i.__)("Course two","learnpress")}];h(e)}return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t.InspectorControls,null,(0,e.createElement)(l.PanelBody,{title:(0,i.__)("Settings","learnpress")},(0,e.createElement)(l.__experimentalToggleGroupControl,{label:(0,i.__)("Layout","learnpress"),value:c.layout||"gird",onChange:e=>{u({layout:e||"gird"})},isBlock:!0},(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"list",label:"Stack"}),(0,e.createElement)(l.__experimentalToggleGroupControlOption,{value:"grid",label:"Grid"})),"grid"===c.layout&&(0,e.createElement)(l.RangeControl,{label:(0,i.__)("Columns","learnpress"),value:c.columns||3,onChange:e=>{u({columns:e})},min:2,max:6,step:1,marks:[{value:2,label:"2"},{value:3,label:"3"},{value:4,label:"4"},{value:5,label:"5"},{value:6,label:"6"}],withInputField:!0}))),(0,e.createElement)("ul",{className:"learn-press-courses wp-block-learn-press-courses","data-layout":c.layout?c.layout:"list",style:{"--columns":c.columns||3}},I&&I.map(r=>(0,e.createElement)(t.BlockContextProvider,{key:r.courseId,value:r},r.courseId===(g||I[0]?.courseId)?(0,e.createElement)(d,{classList:r.classList}):null,(0,e.createElement)(v,{blocks:x,blockContextId:r.courseId,classList:r.classList,setActiveBlockContextId:f,isHidden:r.courseId===(g||I[0]?.courseId)})))),s.lpCourseQuery?.pagination?function(t){switch(t){case"load-more":return(0,e.createElement)("button",{className:"courses-btn-load-more"},(0,i.__)("Load More","learnpress"));case"infinite":return"";default:return(0,e.createElement)("nav",{className:"learnpress-block-pagination navigation pagination"},(0,e.createElement)("ul",{className:"page-numbers"},(0,e.createElement)("li",null,(0,e.createElement)("a",{className:"prev page-numbers",href:"?paged=1"},(0,e.createElement)("i",{className:"lp-icon-arrow-left"}))),Array.from({length:3},(t,r)=>(0,e.createElement)("li",{key:r},(0,e.createElement)("a",{className:"page-numbers",href:"{index}"},r+1)))))}}(k):null)},save:r=>{const s=t.useBlockProps.save();return(0,e.createElement)("div",{...s},(0,e.createElement)(t.InnerBlocks.Content,null))}})})()})();