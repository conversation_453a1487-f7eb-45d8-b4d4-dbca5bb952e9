(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.components,window.wp.blockEditor),r=r=>{const s=(0,t.useBlockProps)();return(0,e.createElement)("div",{...s},(0,e.createElement)("div",{class:"search-course"},(0,e.createElement)("input",{type:"text",name:"s",autocomplete:"off",placeholder:"Search for course content"}),(0,e.createElement)("button",{name:"submit","aria-label":"Search for course content"},(0,e.createElement)("i",{class:"lp-icon-search"}))))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-search","title":"Item Search","category":"learnpress-category","icon":"search","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item search course","learnpress"],"usesContext":[],"supports":{"align":true}}'),o=window.wp.blocks,c=window.wp.data;let a=null;var l,i,p;l=["learnpress/learnpress//single-lp_course_item"],i=n,p=e=>{(0,o.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,c.subscribe)(()=>{const e={...i},t=(0,c.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&a!==r&&(a=r,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),l.includes(r)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,o.registerBlockType)(n.name,{...n,edit:r,save:s})})();