(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const a=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...a},(0,e.createElement)("div",{className:"course-faqs course-tab-panel-faqs"},(0,e.createElement)("h3",{className:"course-faqs__title"},(0,t.__)("FAQs","learnpress")),(0,e.createElement)("div",{className:"course-faqs-box"},(0,e.createElement)("label",{className:"course-faqs-box__title"},"Fortepossumus poterat varietatem nullus tum signa dissentiens abducas gaudio memini pervertere impudens?")),(0,e.createElement)("div",{className:"course-faqs-box"},(0,e.createElement)("label",{className:"course-faqs-box__title"},"Homerus defecerit iure naturales praeponunt futuri avaritiamne celebrari parva vincla aetatulis extrema?")))))},a=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-faqs","title":"Course Faqs","category":"learnpress-course-elements","icon":"index-card","description":"Renders template Box Extra Faqs Course PHP templates.","textdomain":"learnpress","keywords":["box extra faqs single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"align":["wide","full"],"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,o=window.wp.data;let i=null;var u,c,p;u=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],c=n,p=e=>{(0,l.registerBlockType)(e.name,{...e,edit:s,save:a})},(0,o.subscribe)(()=>{const e={...c},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&i!==r&&(i=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,l.registerBlockType)(n.name,{...n,edit:s,save:a})})();