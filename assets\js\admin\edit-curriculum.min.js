import*as lpEditCurriculumShare from"./edit-curriculum/share.js";import*as sectionEdit from"./edit-curriculum/edit-section.js";import*as sectionItemEdit from"./edit-curriculum/edit-section-item.js";const{className:className}=lpEditCurriculumShare,toggleSectionAll=(e,t)=>{const i=t.closest(`${className.elToggleAllSections}`);if(!i)return;const c=lpEditCurriculumShare.elEditCurriculum.querySelectorAll(`${className.elSection}:not(.clone)`);i.classList.toggle(`${className.elCollapse}`),i.classList.contains(`${className.elCollapse}`)?c.forEach(e=>{e.classList.contains(`${className.elCollapse}`)||e.classList.add(`${className.elCollapse}`)}):c.forEach(e=>{e.classList.contains(`${className.elCollapse}`)&&e.classList.remove(`${className.elCollapse}`)})},updateCountItems=e=>{const t=lpEditCurriculumShare.elEditCurriculum,i=t.querySelector(".total-items"),c=t.querySelectorAll(`${className.elSectionItem}:not(.clone)`).length;i.dataset.count=c,i.querySelector(".count").textContent=c;const s=e.querySelector(".section-items-counts"),o=e.querySelectorAll(`${className.elSectionItem}:not(.clone)`).length;s.dataset.count=o,s.querySelector(".count").textContent=o};document.addEventListener("click",e=>{const t=e.target;sectionEdit.addSection(e,t),sectionEdit.setFocusTitleInput(e,t),sectionEdit.toggleSection(e,t),sectionEdit.updateSectionDescription(e,t),sectionEdit.cancelSectionDescription(e,t),sectionEdit.deleteSection(e,t),sectionEdit.updateSectionTitle(e,t),sectionEdit.cancelSectionTitle(e,t),sectionItemEdit.addItemType(e,t),sectionItemEdit.cancelAddItemType(e,t),sectionItemEdit.addItemToSection(e,t),sectionItemEdit.showPopupItemsToSelect(e,t),sectionItemEdit.showItemsSelected(e,t),sectionItemEdit.chooseTabItemsType(e,t),sectionItemEdit.updateTitle(e,t),sectionItemEdit.cancelUpdateTitle(e,t),sectionItemEdit.deleteItem(e,t),sectionItemEdit.selectItemsFromList(e,t),sectionItemEdit.addItemsSelectedToSection(e,t),sectionItemEdit.backToSelectItems(e,t),sectionItemEdit.removeItemSelected(e,t),sectionItemEdit.updatePreviewItem(e,t),toggleSectionAll(0,t)}),document.addEventListener("keydown",e=>{const t=e.target;"Enter"===e.key&&(sectionEdit.addSection(e,t),sectionEdit.updateSectionTitle(e,t),sectionEdit.updateSectionDescription(e,t),sectionItemEdit.addItemToSection(e,t),sectionItemEdit.updateTitle(e,t))}),document.addEventListener("keyup",e=>{const t=e.target;sectionEdit.changeTitleBeforeAdd(e,t),sectionEdit.changeTitle(e,t),sectionEdit.changeDescription(e,t),sectionItemEdit.changeTitle(e,t),sectionItemEdit.changeTitleAddNew(e,t),sectionItemEdit.searchTitleItemToSelect(e,t)}),document.addEventListener("focusin",e=>{sectionEdit.focusTitleNewInput(e,e.target),sectionEdit.focusTitleInput(e,e.target),sectionItemEdit.focusTitleInput(e,e.target)}),document.addEventListener("focusout",e=>{sectionEdit.focusTitleNewInput(e,e.target,!1),sectionEdit.focusTitleInput(e,e.target,!1),sectionItemEdit.focusTitleInput(e,e.target,!1)}),window.addEventListener("beforeunload",function(e){0!==Object.keys(lpEditCurriculumShare.hasChange).length&&(e.preventDefault(),e.returnValue="")}),lpEditCurriculumShare.lpUtils.lpOnElementReady(`${className.idElEditCurriculum}`,e=>{const t=e.querySelector(`${className.elCurriculumSections}`),i=e.closest(`${className.LPTarget}`),c=window.lpAJAXG.getDataSetCurrent(i);lpEditCurriculumShare.setVariables({courseId:c.args.course_id,elEditCurriculum:e,elCurriculumSections:t,elLPTarget:i,updateCountItems:updateCountItems,hasChange:{}}),sectionEdit.init(),sectionEdit.sortAbleSection(),sectionItemEdit.init(),sectionItemEdit.sortAbleItem(),lpEditCurriculumShare.setVariable("sortAbleItem",sectionItemEdit.sortAbleItem)});