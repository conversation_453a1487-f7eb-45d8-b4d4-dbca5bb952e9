import{lpAjaxParseJsonOld}from"../utils";const $=jQuery,$doc=$(document),makePaymentsSortable=function(){$(".learn-press-payments.sortable tbody").sortable({handle:".dashicons-menu",helper:(e,t)=>(t.children().each(function(){$(this).width($(this).width())}),t),axis:"y",start(e,t){},stop(e,t){},update(e,t){const a=$(this).children().map(function(){return $(this).find('input[name="payment-order"]').val()}).get();$.post({url:"",data:{"lp-ajax":"update-payment-order",order:a,nonce:$("input[name=lp-settings-nonce]").val()},success(e){}})}})},lpMetaboxCustomFields=()=>{$(".lp-metabox__custom-fields").on("click",".lp-metabox-custom-field-button",function(){const t=$(this).data("row").replace(/lp_metabox_custom_fields_key/gi,Math.floor(1e3*Math.random())+1);return $(this).closest("table").find("tbody").append(t),e($(this).closest(".lp-metabox__custom-fields")),!1}),$(".lp-metabox__custom-fields").on("click","a.delete",function(){return $(this).closest("tr").remove(),e($(this).closest(".lp-metabox__custom-fields")),!1}),$(".lp-metabox__custom-fields tbody").sortable({items:"tr",cursor:"move",axis:"y",handle:"td.sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,update(t,a){e($(this).closest(".lp-metabox__custom-fields"))}});const e=e=>{e.find("tbody tr").each(function(e,t){$(this).find(".sort .count").val(e)})}},lpMetaboxRepeaterField=()=>{const e=e=>{e.find(".lp_repeater_meta_box__field").each(function(e,t){$(this).find(".lp_repeater_meta_box__field__count").val(e),$(this).find(".lp_repeater_meta_box__title__title > span").text(e+1)})};$(".lp_repeater_meta_box__add").on("click",function(){const t=$(this).data("add").replace(/lp_metabox_repeater_key/gi,Math.floor(1e3*Math.random())+1);return $(this).closest(".lp_repeater_meta_box__wrapper").find(".lp_repeater_meta_box__fields").append(t),e($(this).closest(".lp_repeater_meta_box__wrapper")),$(this).closest(".lp_repeater_meta_box__wrapper").find(".lp_repeater_meta_box__fields").last().find("input").trigger("focus"),!1}),$(".lp_repeater_meta_box__wrapper").on("click","a.lp_repeater_meta_box__title__delete",function(){return $(this).closest(".lp_repeater_meta_box__field").remove(),e($(this).closest(".lp_repeater_meta_box__wrapper")),!1}),$(".lp_repeater_meta_box__fields").on("click",".lp_repeater_meta_box__title__toggle, .lp_repeater_meta_box__title__title",function(){const e=$(this).closest(".lp_repeater_meta_box__field");return e.hasClass("lp_repeater_meta_box__field_active")?e.removeClass("lp_repeater_meta_box__field_active"):e.addClass("lp_repeater_meta_box__field_active"),!1}),$(".lp_repeater_meta_box__fields").sortable({items:".lp_repeater_meta_box__field",cursor:"grab",axis:"y",handle:".lp_repeater_meta_box__title__sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,update(t,a){e($(this).closest(".lp_repeater_meta_box__wrapper"))}})},lpMetaboxExtraInfo=()=>{$(".lp_course_extra_meta_box__add").on("click",function(){return $(this).closest(".lp_course_extra_meta_box__content").find(".lp_course_extra_meta_box__fields").append($(this).data("add")),$(this).closest(".lp_course_extra_meta_box__content").find(".lp_course_extra_meta_box__field").last().find("input").trigger("focus"),!1}),$(".lp_course_extra_meta_box__fields").on("click","a.delete",function(){return $(this).closest(".lp_course_extra_meta_box__field").remove(),!1}),$(".lp_course_extra_meta_box__fields").sortable({items:".lp_course_extra_meta_box__field",cursor:"grab",axis:"y",handle:".sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65}),$(".lp_course_faq_meta_box__add").on("click",function(){return $(this).closest(".lp_course_faq_meta_box__content").find(".lp_course_faq_meta_box__fields").append($(this).data("add")),!1}),$(".lp_course_faq_meta_box__fields").on("click","a.delete",function(){return $(this).closest(".lp_course_faq_meta_box__field").remove(),!1}),$(".lp_course_faq_meta_box__fields").sortable({items:".lp_course_faq_meta_box__field",cursor:"grab",axis:"y",handle:".sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65})},lpGetFinalQuiz=()=>{[...document.querySelectorAll(".lp-metabox-get-final-quiz")].map(t=>{t.addEventListener("click",a=>{a.preventDefault();const s=t.textContent,l=t.dataset.loading,i=document.querySelector(".lp-metabox-evaluate-final_quiz");i&&i.remove(),t.textContent=l,e(t).then(e=>{const{message:a,data:l}=e;t.textContent=s;const i=document.createElement("div");i.className="lp-metabox-evaluate-final_quiz",i.innerHTML=l||a,t.parentNode.insertBefore(i,t.nextSibling)})})});const e=async e=>await wp.apiFetch({path:"lp/v1/admin/course/get_final_quiz",method:"POST",data:{courseId:e.dataset.postid||""}})},lpMetaboxColorPicker=()=>{$(".lp-metabox__colorpick").iris({change(e,t){$(this).parent().find(".colorpickpreview").css({backgroundColor:t.color.toString()})},hide:!0,border:!0}).on("click focus",function(e){e.stopPropagation(),$(".iris-picker").hide(),$(this).closest("td").find(".iris-picker").show(),$(this).data("original-value",$(this).val())}).on("change",function(){if($(this).is(".iris-error")){$(this).data("original-value").match(/^\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)?$(this).val($(this).data("original-value")).trigger("change"):$(this).val("").trigger("change")}}),$("body").on("click",function(){$(".iris-picker").hide()})},lpMetaboxImage=()=>{$(".lp-metabox-field__image").each((e,t)=>{let a;const s=$(t).find(".lp-metabox-field__image--add"),l=$(t).find(".lp-metabox-field__image--delete"),i=$(t).find(".lp-metabox-field__image--image"),o=$(t).find(".lp-metabox-field__image--id");o.val()?(s.hide(),l.show()):(s.show(),l.hide()),s.on("click",e=>{e.preventDefault(),a||(a=wp.media({title:s.data("choose"),button:{text:s.data("update")},multiple:!1}),a.on("select",function(){const e=a.state().get("selection").first().toJSON(),t=e.sizes&&e.sizes.thumbnail?e.sizes.thumbnail.url:e.url;i.append('<div class="lp-metabox-field__image--inner"><img src="'+t+'" alt="" style="max-width:100%;"/></div>'),o.val(e.id),s.hide(),l.show()})),a.open()}),l.on("click",e=>{e.preventDefault(),i.html(""),s.show(),l.hide(),o.val("")})})},lpMetaboxImageAdvanced=()=>{$(".lp-metabox-field__image-advanced").each((e,t)=>{let a;const s=$(t).find("#lp-gallery-images-ids"),l=$(t).find(".lp-metabox-field__image-advanced-images"),i=$(t).find(".lp-metabox-field__image-advanced-upload > a");$(i).on("click",e=>{e.preventDefault(),a||(a=wp.media({title:i.data("choose"),button:{text:i.data("update")},states:[new wp.media.controller.Library({title:i.data("choose"),filterable:"all",multiple:!0})]}),a.on("select",function(){const e=a.state().get("selection");let t=s.val();e.forEach(function(e){if((e=e.toJSON()).id){t=t?t+","+e.id:e.id;const a=e.sizes&&e.sizes.thumbnail?e.sizes.thumbnail.url:e.url;l.append('<li class="image" data-attachment_id="'+e.id+'"><img src="'+a+'" /><ul class="actions"><li><a href="#" class="delete" title="'+i.data("delete")+'">'+i.data("text")+"</a></li></ul></li>")}}),s.val(t)})),a.open()}),l.sortable({items:"li.image",cursor:"move",scrollSensitivity:40,forcePlaceholderSize:!0,forceHelperSize:!1,helper:"clone",opacity:.65,placeholder:"lp-metabox-sortable-placeholder",start(e,t){t.item.css("background-color","#f6f6f6")},stop(e,t){t.item.removeAttr("style")},update(){let e="";l.find("li.image").css("cursor","default").each(function(){const t=$(this).attr("data-attachment_id");e=e+t+","}),s.val(e)}}),$(l).find("li.image").each((e,t)=>{$(t).find("a.delete").on("click",()=>{$(t).remove();let e="";return $(l).find("li.image").css("cursor","default").each(function(){const t=$(this).attr("data-attachment_id");e=e+t+","}),s.val(e),!1})})})},lpMetaboxCourseTabs=()=>{$(document.body).on("lp-metabox-course-tab-panels",function(){$("ul.lp-meta-box__course-tab__tabs").show(),$("ul.lp-meta-box__course-tab__tabs a").on("click",function(e){e.preventDefault();const t=$(this).closest("div.lp-meta-box__course-tab");$("ul.lp-meta-box__course-tab__tabs li",t).removeClass("active"),$(this).parent().addClass("active"),$("div.lp-meta-box-course-panels",t).hide(),$($(this).attr("href")).show()}),$("div.lp-meta-box__course-tab").each(function(){$(this).find("ul.lp-meta-box__course-tab__tabs li").eq(0).find("a").trigger("click")})}).trigger("lp-metabox-course-tab-panels")},initTooltips=function(){$(".learn-press-tooltip").each(function(){const e=$(this),t=$.extend({title:"data-tooltip",offset:10,gravity:"s"},e.data());e.tipsy(t)})},initSelect2=function(){if($.fn.select2){const e=$(".lp-select-2 select");e.select2({placeholder:"Select a value"}),e.on("change.select2",function(e){const t=$(e.target);t.val().length||t.val(null)}),$(".lp_autocomplete_metabox_field").each(function(){const e=$(this).data("atts");let t=e.action;if(!t)if("users"===e.data)t=e.rest_url+"wp/v2/users";else t=e.rest_url+"wp/v2/"+e.data;$(this).find("select").select2({placeholder:e.placeholder?e.placeholder:"Select",ajax:{url:t,dataType:"json",delay:250,beforeSend(t){t.setRequestHeader("X-WP-Nonce",e.nonce)},data:e=>({search:e.term}),processResults:e=>({results:e.map(e=>({id:e.id,text:e.title&&e.title.rendered?e.title.rendered:e.name}))}),cache:!0},minimumInputLength:2})})}},initSingleCoursePermalink=function(){$doc.on("change",'.learn-press-single-course-permalink input[type="radio"]',function(){const e=$(this).closest(".learn-press-single-course-permalink");e.hasClass("custom-base")?e.find('input[type="text"]').prop("readonly",!1):e.siblings(".custom-base").find('input[type="text"]').prop("readonly",!0)}).on("change","input.learn-press-course-base",function(){$("#course_permalink_structure").val($(this).val())}).on("focus","#course_permalink_structure",function(){$("#learn_press_custom_permalink").click()}).on("change","#learn_press_courses_page_id",function(){$("tr.learn-press-courses-page-id").toggleClass("hide-if-js",!parseInt(this.value))})},togglePaymentStatus=function(e){e.preventDefault();const t=$(this).closest("tr"),a=($(this),t.find(".status").hasClass("enabled")?"no":"yes");$.ajax({url:"",data:{"lp-ajax":"update-payment-status",status:a,id:t.data("payment"),nonce:$("input[name=lp-settings-nonce]").val()},success(e){e=lpAjaxParseJsonOld(e);for(const t in e)$("#payment-"+t+" .status").toggleClass("enabled",e[t])}})},updateEmailStatus=function(){(function(){$.post({url:window.location.href,data:{"lp-ajax":"update_email_status",status:$(this).parent().hasClass("enabled")?"no":"yes",id:$(this).data("id"),nonce:$("input[name=lp-settings-nonce]").val()},dataType:"text",success:$.proxy(function(e){e=lpAjaxParseJsonOld(e);for(const t in e)$("#email-"+t+" .status").toggleClass("enabled",e[t])},this)})}).apply(this)},lpMetaboxsalePriceDate=()=>{$("#course-settings").length&&($(".lp_sale_dates_fields").each(function(){const e=$(this).closest("#price_course_data");let t=!1;$(this).find("input").each(function(){""!==$(this).val()&&(t=!0)}),t?(e.find(".lp_sale_price_schedule").hide(),e.find(".lp_sale_dates_fields").show()):(e.find(".lp_sale_price_schedule").show(),e.find(".lp_sale_dates_fields").hide())}),$(".lp-meta-box-course-panels").on("click",".lp_sale_price_schedule",function(){const e=$(this).closest("#price_course_data");return $(this).hide(),e.find(".lp_cancel_sale_schedule").show(),e.find(".lp_sale_dates_fields").show(),!1}),$(".lp-meta-box-course-panels").on("click",".lp_cancel_sale_schedule",function(){const e=$(this).closest("div.lp-meta-box-course-panels");return $(this).hide(),e.find(".lp_sale_price_schedule").show(),e.find(".lp_sale_dates_fields").hide(),e.find(".lp_sale_dates_fields").find("input").val(""),!1}),$(document).on("input","#price_course_data",function(e){const t=$(this),a=$(".lp_meta_box_regular_price"),s=$(".lp_meta_box_sale_price"),l=$(e.target).attr("id");t.find(".learn-press-tip-floating").remove(),parseInt(s.val())>parseInt(a.val())&&("_lp_price"===l?a.parent(".form-field").append('<div class="learn-press-tip-floating">'+lpAdminCourseEditorSettings.i18n.notice_price+"</div>"):"_lp_sale_price"===l&&s.parent(".form-field").append('<div class="learn-press-tip-floating">'+lpAdminCourseEditorSettings.i18n.notice_sale_price+"</div>"))}))},lpHidePassingGrade=()=>{const e=["evaluate_final_quiz","evaluate_final_assignment"];[...document.querySelectorAll("input[type=radio][name=_lp_course_result]")].map((t,a)=>(t.checked&&e.includes(t.value)&&$("._lp_passing_condition_field").hide(),null)),$("input[type=radio][name=_lp_course_result]").on("change",function(t){e.includes(t.target.value)?$("._lp_passing_condition_field").hide():$("._lp_passing_condition_field").show()})},callbackFilterTemplates=function(){const e=$(this);if(e.hasClass("current"))return!1;const t=$("#learn-press-template-files"),a=t.find("tr[data-template]"),s=e.data("template"),l=e.data("filter");return e.addClass("current").siblings("a").removeClass("current"),s?a.map(function(){$(this).toggleClass("hide-if-js",$(this).data("template")!==s)}):l?a.map(function(){$(this).toggleClass("hide-if-js","yes"!==$(this).data("filter-"+l))}):a.removeClass("hide-if-js"),$("#learn-press-no-templates").toggleClass("hide-if-js",!!t.find("tr.template-row:not(.hide-if-js):first").length),!1},toggleEmails=function(e){e.preventDefault();const t=$(this).data("status");$.ajax({url:"",data:{"lp-ajax":"update_email_status",nonce:$("input[name=lp-settings-nonce]").val(),status:t},success(e){e=lpAjaxParseJsonOld(e);for(const t in e)$("#email-"+t+" .status").toggleClass("enabled",e[t])}})},onReady=function(){makePaymentsSortable(),initSelect2(),initTooltips(),initSingleCoursePermalink(),lpMetaboxCourseTabs(),lpMetaboxCustomFields(),lpMetaboxColorPicker(),lpMetaboxImageAdvanced(),lpMetaboxImage(),lpMetaboxsalePriceDate(),lpMetaboxExtraInfo(),lpHidePassingGrade(),lpGetFinalQuiz(),lpMetaboxRepeaterField(),$(document).on("click",".learn-press-payments .status .dashicons",togglePaymentStatus).on("click",".change-email-status",updateEmailStatus).on("click",".learn-press-filter-template",callbackFilterTemplates).on("click","#learn-press-enable-emails, #learn-press-disable-emails",toggleEmails)};$(document).ready(onReady),document.addEventListener("keydown",function(e){const t=e.target;"Enter"!==e.key&&13!==e.keyCode||(t.classList.contains("lp_course_extra_meta_box__input")||"INPUT"===t.tagName&&t.closest(".lp_course_faq_meta_box__field"))&&(e.preventDefault(),t.blur())});