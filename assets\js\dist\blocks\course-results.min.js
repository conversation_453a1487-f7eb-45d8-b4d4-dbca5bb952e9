(()=>{"use strict";const e=window.React,t=window.wp.i18n,s=window.wp.blockEditor,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-results","title":"Course Results","category":"learnpress-course-elements","icon":"feedback","description":"Show Course Results.","textdomain":"learnpress","keywords":["course results","learnpress"],"ancestor":["learnpress/list-courses"],"usesContext":[],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":false,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}');(0,window.wp.blocks.registerBlockType)(r.name,{...r,edit:r=>{const l=(0,s.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...l},(0,e.createElement)("div",{class:"course-results"},(0,t.sprintf)((0,t.__)("Showing %d-%d of %d results","learnpress"),1,6,20))))},save:e=>null})})();