(()=>{var e={9455:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var n=o(1601),i=o.n(n),s=o(6314),r=o.n(s)()(i());r.push([e.id,"/*!\n * Toastify js 1.12.0\n * https://github.com/apvarun/toastify-js\n * @license MIT licensed\n *\n * Copyright (C) 2018 Varun A P\n */\n\n.toastify {\n    padding: 12px 20px;\n    color: #ffffff;\n    display: inline-block;\n    box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(77, 96, 232, 0.3);\n    background: -webkit-linear-gradient(315deg, #73a5ff, #5477f5);\n    background: linear-gradient(135deg, #73a5ff, #5477f5);\n    position: fixed;\n    opacity: 0;\n    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);\n    border-radius: 2px;\n    cursor: pointer;\n    text-decoration: none;\n    max-width: calc(50% - 20px);\n    z-index: **********;\n}\n\n.toastify.on {\n    opacity: 1;\n}\n\n.toast-close {\n    background: transparent;\n    border: 0;\n    color: white;\n    cursor: pointer;\n    font-family: inherit;\n    font-size: 1em;\n    opacity: 0.4;\n    padding: 0 5px;\n}\n\n.toastify-right {\n    right: 15px;\n}\n\n.toastify-left {\n    left: 15px;\n}\n\n.toastify-top {\n    top: -150px;\n}\n\n.toastify-bottom {\n    bottom: -150px;\n}\n\n.toastify-rounded {\n    border-radius: 25px;\n}\n\n.toastify-avatar {\n    width: 1.5em;\n    height: 1.5em;\n    margin: -7px 5px;\n    border-radius: 2px;\n}\n\n.toastify-center {\n    margin-left: auto;\n    margin-right: auto;\n    left: 0;\n    right: 0;\n    max-width: fit-content;\n    max-width: -moz-fit-content;\n}\n\n@media only screen and (max-width: 360px) {\n    .toastify-right, .toastify-left {\n        margin-left: auto;\n        margin-right: auto;\n        left: 0;\n        right: 0;\n        max-width: fit-content;\n    }\n}\n",""]);const a=r},6314:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var o="",n=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),n&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),n&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o}).join("")},t.i=function(e,o,n,i,s){"string"==typeof e&&(e=[[null,e,void 0]]);var r={};if(n)for(var a=0;a<this.length;a++){var l=this[a][0];null!=l&&(r[l]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);n&&r[d[0]]||(void 0!==s&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=s),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),i&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=i):d[4]="".concat(i)),t.push(d))}},t}},1601:e=>{"use strict";e.exports=function(e){return e[1]}},5072:e=>{"use strict";var t=[];function o(e){for(var o=-1,n=0;n<t.length;n++)if(t[n].identifier===e){o=n;break}return o}function n(e,n){for(var s={},r=[],a=0;a<e.length;a++){var l=e[a],c=n.base?l[0]+n.base:l[0],d=s[c]||0,u="".concat(c," ").concat(d);s[c]=d+1;var p=o(u),m={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==p)t[p].references++,t[p].updater(m);else{var h=i(m,n);n.byIndex=a,t.splice(a,0,{identifier:u,updater:h,references:1})}r.push(u)}return r}function i(e,t){var o=t.domAPI(t);return o.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,i){var s=n(e=e||[],i=i||{});return function(e){e=e||[];for(var r=0;r<s.length;r++){var a=o(s[r]);t[a].references--}for(var l=n(e,i),c=0;c<s.length;c++){var d=o(s[c]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}s=l}}},7659:e=>{"use strict";var t={};e.exports=function(e,o){var n=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(o)}},540:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},5056:(e,t,o)=>{"use strict";e.exports=function(e){var t=o.nc;t&&e.setAttribute("nonce",t)}},7825:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(o){!function(e,t,o){var n="";o.supports&&(n+="@supports (".concat(o.supports,") {")),o.media&&(n+="@media ".concat(o.media," {"));var i=void 0!==o.layer;i&&(n+="@layer".concat(o.layer.length>0?" ".concat(o.layer):""," {")),n+=o.css,i&&(n+="}"),o.media&&(n+="}"),o.supports&&(n+="}");var s=o.sourceMap;s&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,o)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},1113:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},7736:function(e){var t;t=function(e){var t=function(e){return new t.lib.init(e)};function o(e,t){return t.offset[e]?isNaN(t.offset[e])?t.offset[e]:t.offset[e]+"px":"0px"}function n(e,t){return!(!e||"string"!=typeof t||!(e.className&&e.className.trim().split(/\s+/gi).indexOf(t)>-1))}return t.defaults={oldestFirst:!0,text:"Toastify is awesome!",node:void 0,duration:3e3,selector:void 0,callback:function(){},destination:void 0,newWindow:!1,close:!1,gravity:"toastify-top",positionLeft:!1,position:"",backgroundColor:"",avatar:"",className:"",stopOnFocus:!0,onClick:function(){},offset:{x:0,y:0},escapeMarkup:!0,ariaLive:"polite",style:{background:""}},t.lib=t.prototype={toastify:"1.12.0",constructor:t,init:function(e){return e||(e={}),this.options={},this.toastElement=null,this.options.text=e.text||t.defaults.text,this.options.node=e.node||t.defaults.node,this.options.duration=0===e.duration?0:e.duration||t.defaults.duration,this.options.selector=e.selector||t.defaults.selector,this.options.callback=e.callback||t.defaults.callback,this.options.destination=e.destination||t.defaults.destination,this.options.newWindow=e.newWindow||t.defaults.newWindow,this.options.close=e.close||t.defaults.close,this.options.gravity="bottom"===e.gravity?"toastify-bottom":t.defaults.gravity,this.options.positionLeft=e.positionLeft||t.defaults.positionLeft,this.options.position=e.position||t.defaults.position,this.options.backgroundColor=e.backgroundColor||t.defaults.backgroundColor,this.options.avatar=e.avatar||t.defaults.avatar,this.options.className=e.className||t.defaults.className,this.options.stopOnFocus=void 0===e.stopOnFocus?t.defaults.stopOnFocus:e.stopOnFocus,this.options.onClick=e.onClick||t.defaults.onClick,this.options.offset=e.offset||t.defaults.offset,this.options.escapeMarkup=void 0!==e.escapeMarkup?e.escapeMarkup:t.defaults.escapeMarkup,this.options.ariaLive=e.ariaLive||t.defaults.ariaLive,this.options.style=e.style||t.defaults.style,e.backgroundColor&&(this.options.style.background=e.backgroundColor),this},buildToast:function(){if(!this.options)throw"Toastify is not initialized";var e=document.createElement("div");for(var t in e.className="toastify on "+this.options.className,this.options.position?e.className+=" toastify-"+this.options.position:!0===this.options.positionLeft?(e.className+=" toastify-left",console.warn("Property `positionLeft` will be depreciated in further versions. Please use `position` instead.")):e.className+=" toastify-right",e.className+=" "+this.options.gravity,this.options.backgroundColor&&console.warn('DEPRECATION NOTICE: "backgroundColor" is being deprecated. Please use the "style.background" property.'),this.options.style)e.style[t]=this.options.style[t];if(this.options.ariaLive&&e.setAttribute("aria-live",this.options.ariaLive),this.options.node&&this.options.node.nodeType===Node.ELEMENT_NODE)e.appendChild(this.options.node);else if(this.options.escapeMarkup?e.innerText=this.options.text:e.innerHTML=this.options.text,""!==this.options.avatar){var n=document.createElement("img");n.src=this.options.avatar,n.className="toastify-avatar","left"==this.options.position||!0===this.options.positionLeft?e.appendChild(n):e.insertAdjacentElement("afterbegin",n)}if(!0===this.options.close){var i=document.createElement("button");i.type="button",i.setAttribute("aria-label","Close"),i.className="toast-close",i.innerHTML="&#10006;",i.addEventListener("click",function(e){e.stopPropagation(),this.removeElement(this.toastElement),window.clearTimeout(this.toastElement.timeOutValue)}.bind(this));var s=window.innerWidth>0?window.innerWidth:screen.width;("left"==this.options.position||!0===this.options.positionLeft)&&s>360?e.insertAdjacentElement("afterbegin",i):e.appendChild(i)}if(this.options.stopOnFocus&&this.options.duration>0){var r=this;e.addEventListener("mouseover",function(t){window.clearTimeout(e.timeOutValue)}),e.addEventListener("mouseleave",function(){e.timeOutValue=window.setTimeout(function(){r.removeElement(e)},r.options.duration)})}if(void 0!==this.options.destination&&e.addEventListener("click",function(e){e.stopPropagation(),!0===this.options.newWindow?window.open(this.options.destination,"_blank"):window.location=this.options.destination}.bind(this)),"function"==typeof this.options.onClick&&void 0===this.options.destination&&e.addEventListener("click",function(e){e.stopPropagation(),this.options.onClick()}.bind(this)),"object"==typeof this.options.offset){var a=o("x",this.options),l=o("y",this.options),c="left"==this.options.position?a:"-"+a,d="toastify-top"==this.options.gravity?l:"-"+l;e.style.transform="translate("+c+","+d+")"}return e},showToast:function(){var e;if(this.toastElement=this.buildToast(),!(e="string"==typeof this.options.selector?document.getElementById(this.options.selector):this.options.selector instanceof HTMLElement||"undefined"!=typeof ShadowRoot&&this.options.selector instanceof ShadowRoot?this.options.selector:document.body))throw"Root element is not defined";var o=t.defaults.oldestFirst?e.firstChild:e.lastChild;return e.insertBefore(this.toastElement,o),t.reposition(),this.options.duration>0&&(this.toastElement.timeOutValue=window.setTimeout(function(){this.removeElement(this.toastElement)}.bind(this),this.options.duration)),this},hideToast:function(){this.toastElement.timeOutValue&&clearTimeout(this.toastElement.timeOutValue),this.removeElement(this.toastElement)},removeElement:function(e){e.className=e.className.replace(" on",""),window.setTimeout(function(){this.options.node&&this.options.node.parentNode&&this.options.node.parentNode.removeChild(this.options.node),e.parentNode&&e.parentNode.removeChild(e),this.options.callback.call(e),t.reposition()}.bind(this),400)}},t.reposition=function(){for(var e,t={top:15,bottom:15},o={top:15,bottom:15},i={top:15,bottom:15},s=document.getElementsByClassName("toastify"),r=0;r<s.length;r++){e=!0===n(s[r],"toastify-top")?"toastify-top":"toastify-bottom";var a=s[r].offsetHeight;e=e.substr(9,e.length-1),(window.innerWidth>0?window.innerWidth:screen.width)<=360?(s[r].style[e]=i[e]+"px",i[e]+=a+15):!0===n(s[r],"toastify-left")?(s[r].style[e]=t[e]+"px",t[e]+=a+15):(s[r].style[e]=o[e]+"px",o[e]+=a+15)}return this},t.lib.init.prototype=t.lib,t},e.exports?e.exports=t():this.Toastify=t()}},t={};function o(n){var i=t[n];if(void 0!==i)return i.exports;var s=t[n]={id:n,exports:{}};return e[n].call(s.exports,s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0,(()=>{"use strict";var e={};o.r(e),o.d(e,{listenElementCreated:()=>l,listenElementViewed:()=>a,lpAddQueryArgs:()=>r,lpAjaxParseJsonOld:()=>d,lpClassName:()=>n,lpFetchAPI:()=>i,lpGetCurrentURLNoParam:()=>s,lpOnElementReady:()=>c,lpSetLoadingEl:()=>p,lpShowHideEl:()=>u});var t={};o.r(t),o.d(t,{s7:()=>O,$3:()=>I,hV:()=>$,$g:()=>_,HE:()=>B,EO:()=>e,BB:()=>H,C4:()=>q,P0:()=>N,C7:()=>P,P9:()=>D});const n={hidden:"lp-hidden",loading:"loading"},i=(e,t={},o={})=>{"function"==typeof o.before&&o.before(),fetch(e,{method:"GET",...t}).then(e=>e.json()).then(e=>{"function"==typeof o.success&&o.success(e)}).catch(e=>{"function"==typeof o.error&&o.error(e)}).finally(()=>{"function"==typeof o.completed&&o.completed()})},s=()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e},r=(e,t)=>{const o=new URL(e);return Object.keys(t).forEach(e=>{o.searchParams.set(e,t[e])}),o},a=(e,t)=>{new IntersectionObserver(function(e){for(const o of e)o.isIntersecting&&t(o)}).observe(e)},l=e=>{new MutationObserver(function(t){t.forEach(function(t){t.addedNodes&&t.addedNodes.forEach(function(t){1===t.nodeType&&e(t)})})}).observe(document,{childList:!0,subtree:!0})},c=(e,t)=>{const o=document.querySelector(e);if(o)return void t(o);const n=new MutationObserver((o,n)=>{const i=document.querySelector(e);i&&(n.disconnect(),t(i))});n.observe(document.documentElement,{childList:!0,subtree:!0})},d=e=>{if("string"!=typeof e)return e;const t=String.raw({raw:e}).match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);try{e=t?JSON.parse(t[1].replace(/(?:\r\n|\r|\n)/g,"")):JSON.parse(e)}catch(t){e={}}return e},u=(e,t=0)=>{e&&(t?e.classList.remove(n.hidden):e.classList.add(n.hidden))},p=(e,t)=>{e&&(t?e.classList.add(n.loading):e.classList.remove(n.loading))};var m=o(7736),h=o.n(m),w=o(5072),f=o.n(w),g=o(7825),v=o.n(g),b=o(7659),y=o.n(b),S=o(5056),E=o.n(S),x=o(540),C=o.n(x),k=o(1113),A=o.n(k),T=o(9455),L={};let I,_,$,D,P,B;L.styleTagTransform=A(),L.setAttributes=E(),L.insert=y().bind(null,"head"),L.domAPI=v(),L.insertStyleElement=C(),f()(T.A,L),T.A&&T.A.locals&&T.A.locals;const O={idElEditCurriculum:"#lp-course-edit-curriculum",elCurriculumSections:".curriculum-sections",elSection:".section",elToggleAllSections:".course-toggle-all-sections",elSectionItem:".section-item",LPTarget:".lp-target",elCollapse:"lp-collapse"},M={text:"",gravity:lpDataAdmin.toast.gravity,position:lpDataAdmin.toast.position,className:`${lpDataAdmin.toast.classPrefix}`,close:1==lpDataAdmin.toast.close,stopOnFocus:1==lpDataAdmin.toast.stopOnFocus,duration:lpDataAdmin.toast.duration},N=(e,t="success")=>{new(h())({...M,text:e,className:`${lpDataAdmin.toast.classPrefix} ${t}`}).showToast()},q=e=>{({courseId:I,elEditCurriculum:_,elCurriculumSections:$,updateCountItems:D,hasChange:B}=e)},H=(e,t)=>{e&&void 0!==t&&"sortAbleItem"===e&&(P=t)};function j(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}function X(e,t){return e.get(j(e,t))}const z={},R="swal2-",F=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"].reduce((e,t)=>(e[t]=R+t,e),{}),V=["success","warning","info","question","error"].reduce((e,t)=>(e[t]=R+t,e),{}),Y="SweetAlert2:",U=e=>e.charAt(0).toUpperCase()+e.slice(1),W=e=>{console.warn(`${Y} ${"object"==typeof e?e.join(" "):e}`)},J=e=>{console.error(`${Y} ${e}`)},G=[],Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;var o;o=`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`,G.includes(o)||(G.push(o),W(o))},K=e=>"function"==typeof e?e():e,Q=e=>e&&"function"==typeof e.toPromise,ee=e=>Q(e)?e.toPromise():Promise.resolve(e),te=e=>e&&Promise.resolve(e)===e,oe=()=>document.body.querySelector(`.${F.container}`),ne=e=>{const t=oe();return t?t.querySelector(e):null},ie=e=>ne(`.${e}`),se=()=>ie(F.popup),re=()=>ie(F.icon),ae=()=>ie(F.title),le=()=>ie(F["html-container"]),ce=()=>ie(F.image),de=()=>ie(F["progress-steps"]),ue=()=>ie(F["validation-message"]),pe=()=>ne(`.${F.actions} .${F.confirm}`),me=()=>ne(`.${F.actions} .${F.cancel}`),he=()=>ne(`.${F.actions} .${F.deny}`),we=()=>ne(`.${F.loader}`),fe=()=>ie(F.actions),ge=()=>ie(F.footer),ve=()=>ie(F["timer-progress-bar"]),be=()=>ie(F.close),ye=()=>{const e=se();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),o=Array.from(t).sort((e,t)=>{const o=parseInt(e.getAttribute("tabindex")||"0"),n=parseInt(t.getAttribute("tabindex")||"0");return o>n?1:o<n?-1:0}),n=e.querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n'),i=Array.from(n).filter(e=>"-1"!==e.getAttribute("tabindex"));return[...new Set(o.concat(i))].filter(e=>qe(e))},Se=()=>Ce(document.body,F.shown)&&!Ce(document.body,F["toast-shown"])&&!Ce(document.body,F["no-backdrop"]),Ee=()=>{const e=se();return!!e&&Ce(e,F.toast)},xe=(e,t)=>{if(e.textContent="",t){const o=(new DOMParser).parseFromString(t,"text/html"),n=o.querySelector("head");n&&Array.from(n.childNodes).forEach(t=>{e.appendChild(t)});const i=o.querySelector("body");i&&Array.from(i.childNodes).forEach(t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)})}},Ce=(e,t)=>{if(!t)return!1;const o=t.split(/\s+/);for(let t=0;t<o.length;t++)if(!e.classList.contains(o[t]))return!1;return!0},ke=(e,t,o)=>{if(((e,t)=>{Array.from(e.classList).forEach(o=>{Object.values(F).includes(o)||Object.values(V).includes(o)||Object.values(t.showClass||{}).includes(o)||e.classList.remove(o)})})(e,t),!t.customClass)return;const n=t.customClass[o];n&&("string"==typeof n||n.forEach?Ie(e,n):W(`Invalid type of customClass.${o}! Expected string or iterable object, got "${typeof n}"`))},Ae=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${F.popup} > .${F[t]}`);case"checkbox":return e.querySelector(`.${F.popup} > .${F.checkbox} input`);case"radio":return e.querySelector(`.${F.popup} > .${F.radio} input:checked`)||e.querySelector(`.${F.popup} > .${F.radio} input:first-child`);case"range":return e.querySelector(`.${F.popup} > .${F.range} input`);default:return e.querySelector(`.${F.popup} > .${F.input}`)}},Te=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},Le=(e,t,o)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(t=>{Array.isArray(e)?e.forEach(e=>{o?e.classList.add(t):e.classList.remove(t)}):o?e.classList.add(t):e.classList.remove(t)}))},Ie=(e,t)=>{Le(e,t,!0)},_e=(e,t)=>{Le(e,t,!1)},$e=(e,t)=>{const o=Array.from(e.children);for(let e=0;e<o.length;e++){const n=o[e];if(n instanceof HTMLElement&&Ce(n,t))return n}},De=(e,t,o)=>{o===`${parseInt(o)}`&&(o=parseInt(o)),o||0===parseInt(o)?e.style.setProperty(t,"number"==typeof o?`${o}px`:o):e.style.removeProperty(t)},Pe=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e&&(e.style.display=t)},Be=e=>{e&&(e.style.display="none")},Oe=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"block";e&&new MutationObserver(()=>{Ne(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},Me=(e,t,o,n)=>{const i=e.querySelector(t);i&&i.style.setProperty(o,n)},Ne=function(e,t){t?Pe(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flex"):Be(e)},qe=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),He=e=>!!(e.scrollHeight>e.clientHeight),je=e=>{const t=window.getComputedStyle(e),o=parseFloat(t.getPropertyValue("animation-duration")||"0"),n=parseFloat(t.getPropertyValue("transition-duration")||"0");return o>0||n>0},Xe=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=ve();o&&qe(o)&&(t&&(o.style.transition="none",o.style.width="100%"),setTimeout(()=>{o.style.transition=`width ${e/1e3}s linear`,o.style.width="0%"},10))},ze=`\n <div aria-labelledby="${F.title}" aria-describedby="${F["html-container"]}" class="${F.popup}" tabindex="-1">\n   <button type="button" class="${F.close}"></button>\n   <ul class="${F["progress-steps"]}"></ul>\n   <div class="${F.icon}"></div>\n   <img class="${F.image}" />\n   <h2 class="${F.title}" id="${F.title}"></h2>\n   <div class="${F["html-container"]}" id="${F["html-container"]}"></div>\n   <input class="${F.input}" id="${F.input}" />\n   <input type="file" class="${F.file}" />\n   <div class="${F.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${F.select}" id="${F.select}"></select>\n   <div class="${F.radio}"></div>\n   <label class="${F.checkbox}">\n     <input type="checkbox" id="${F.checkbox}" />\n     <span class="${F.label}"></span>\n   </label>\n   <textarea class="${F.textarea}" id="${F.textarea}"></textarea>\n   <div class="${F["validation-message"]}" id="${F["validation-message"]}"></div>\n   <div class="${F.actions}">\n     <div class="${F.loader}"></div>\n     <button type="button" class="${F.confirm}"></button>\n     <button type="button" class="${F.deny}"></button>\n     <button type="button" class="${F.cancel}"></button>\n   </div>\n   <div class="${F.footer}"></div>\n   <div class="${F["timer-progress-bar-container"]}">\n     <div class="${F["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),Re=()=>{z.currentInstance.resetValidationMessage()},Fe=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?Ve(e,t):e&&xe(t,e)},Ve=(e,t)=>{e.jquery?Ye(t,e):xe(t,e.toString())},Ye=(e,t)=>{if(e.textContent="",0 in t)for(let o=0;o in t;o++)e.appendChild(t[o].cloneNode(!0));else e.appendChild(t.cloneNode(!0))};function Ue(e,t,o){const n=U(t);Ne(e,o[`show${n}Button`],"inline-block"),xe(e,o[`${t}ButtonText`]||""),e.setAttribute("aria-label",o[`${t}ButtonAriaLabel`]||""),e.className=F[t],ke(e,o,`${t}Button`)}var We={innerParams:new WeakMap,domCache:new WeakMap};const Je=["input","file","range","select","radio","checkbox","textarea"],Ge=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},Ze=(e,t,o)=>{if(o.inputLabel){const n=document.createElement("label"),i=F["input-label"];n.setAttribute("for",e.id),n.className=i,"object"==typeof o.customClass&&Ie(n,o.customClass.inputLabel),n.innerText=o.inputLabel,t.insertAdjacentElement("beforebegin",n)}},Ke=e=>{const t=se();if(t)return $e(t,F[e]||F.input)},Qe=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:te(t)||W(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},et={};et.text=et.email=et.password=et.number=et.tel=et.url=et.search=et.date=et["datetime-local"]=et.time=et.week=et.month=(e,t)=>(Qe(e,t.inputValue),Ze(e,e,t),Ge(e,t),e.type=t.input,e),et.file=(e,t)=>(Ze(e,e,t),Ge(e,t),e),et.range=(e,t)=>{const o=e.querySelector("input"),n=e.querySelector("output");return Qe(o,t.inputValue),o.type=t.input,Qe(n,t.inputValue),Ze(o,e,t),e},et.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const o=document.createElement("option");xe(o,t.inputPlaceholder),o.value="",o.disabled=!0,o.selected=!0,e.appendChild(o)}return Ze(e,e,t),e},et.radio=e=>(e.textContent="",e),et.checkbox=(e,t)=>{const o=Ae(se(),"checkbox");o.value="1",o.checked=Boolean(t.inputValue);const n=e.querySelector("span");return xe(n,t.inputPlaceholder||t.inputLabel),o},et.textarea=(e,t)=>(Qe(e,t.inputValue),Ge(e,t),Ze(e,e,t),setTimeout(()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(se()).width);new MutationObserver(()=>{if(!document.body.contains(e))return;const n=e.offsetWidth+(i=e,parseInt(window.getComputedStyle(i).marginLeft)+parseInt(window.getComputedStyle(i).marginRight));var i;n>o?se().style.width=`${n}px`:De(se(),"width",t.width)}).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e);const tt=(e,t)=>{const o=le();o&&(Oe(o),ke(o,t,"htmlContainer"),t.html?(Fe(t.html,o),Pe(o,"block")):t.text?(o.textContent=t.text,Pe(o,"block")):Be(o),((e,t)=>{const o=se();if(!o)return;const n=We.innerParams.get(e),i=!n||t.input!==n.input;Je.forEach(e=>{const n=$e(o,F[e]);n&&(((e,t)=>{const o=se();if(!o)return;const n=Ae(o,e);if(n){(e=>{for(let t=0;t<e.attributes.length;t++){const o=e.attributes[t].name;["id","type","value","style"].includes(o)||e.removeAttribute(o)}})(n);for(const e in t)n.setAttribute(e,t[e])}})(e,t.inputAttributes),n.className=F[e],i&&Be(n))}),t.input&&(i&&(e=>{if(!e.input)return;if(!et[e.input])return void J(`Unexpected type of input! Expected ${Object.keys(et).join(" | ")}, got "${e.input}"`);const t=Ke(e.input);if(!t)return;const o=et[e.input](t,e);Pe(t),e.inputAutoFocus&&setTimeout(()=>{Te(o)})})(t),(e=>{if(!e.input)return;const t=Ke(e.input);t&&ke(t,e,"input")})(t))})(e,t))},ot=(e,t)=>{for(const[o,n]of Object.entries(V))t.icon!==o&&_e(e,n);Ie(e,t.icon&&V[t.icon]),st(e,t),nt(),ke(e,t,"icon")},nt=()=>{const e=se();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),o=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<o.length;e++)o[e].style.backgroundColor=t},it=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let o=e.innerHTML,n="";t.iconHtml?n=rt(t.iconHtml):"success"===t.icon?(n='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',o=o.replace(/ style=".*?"/g,"")):"error"===t.icon?n='\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':t.icon&&(n=rt({question:"?",warning:"!",info:"i"}[t.icon])),o.trim()!==n.trim()&&xe(e,n)},st=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const o of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])Me(e,o,"background-color",t.iconColor);Me(e,".swal2-success-ring","border-color",t.iconColor)}},rt=e=>`<div class="${F["icon-content"]}">${e}</div>`;let at=!1,lt=0,ct=0,dt=0,ut=0;const pt=e=>{const t=se();if(e.target===t||re().contains(e.target)){at=!0;const o=wt(e);lt=o.clientX,ct=o.clientY,dt=parseInt(t.style.insetInlineStart)||0,ut=parseInt(t.style.insetBlockStart)||0,Ie(t,"swal2-dragging")}},mt=e=>{const t=se();if(at){let{clientX:o,clientY:n}=wt(e);t.style.insetInlineStart=`${dt+(o-lt)}px`,t.style.insetBlockStart=`${ut+(n-ct)}px`}},ht=()=>{const e=se();at=!1,_e(e,"swal2-dragging")},wt=e=>{let t=0,o=0;return e.type.startsWith("mouse")?(t=e.clientX,o=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,o=e.touches[0].clientY),{clientX:t,clientY:o}},ft=(e,t)=>{const o=t.showClass||{};e.className=`${F.popup} ${qe(e)?o.popup:""}`,t.toast?(Ie([document.documentElement,document.body],F["toast-shown"]),Ie(e,F.toast)):Ie(e,F.modal),ke(e,t,"popup"),"string"==typeof t.customClass&&Ie(e,t.customClass),t.icon&&Ie(e,F[`icon-${t.icon}`])},gt=(e,t)=>{((e,t)=>{const o=oe(),n=se();if(o&&n){if(t.toast){De(o,"width",t.width),n.style.width="100%";const e=we();e&&n.insertBefore(e,re())}else De(n,"width",t.width);De(n,"padding",t.padding),t.color&&(n.style.color=t.color),t.background&&(n.style.background=t.background),Be(ue()),ft(n,t),t.draggable&&!t.toast?(Ie(n,F.draggable),(e=>{e.addEventListener("mousedown",pt),document.body.addEventListener("mousemove",mt),e.addEventListener("mouseup",ht),e.addEventListener("touchstart",pt),document.body.addEventListener("touchmove",mt),e.addEventListener("touchend",ht)})(n)):(_e(n,F.draggable),(e=>{e.removeEventListener("mousedown",pt),document.body.removeEventListener("mousemove",mt),e.removeEventListener("mouseup",ht),e.removeEventListener("touchstart",pt),document.body.removeEventListener("touchmove",mt),e.removeEventListener("touchend",ht)})(n))}})(0,t),((e,t)=>{const o=oe();o&&(function(e,t){"string"==typeof t?e.style.background=t:t||Ie([document.documentElement,document.body],F["no-backdrop"])}(o,t.backdrop),function(e,t){t&&(t in F?Ie(e,F[t]):(W('The "position" parameter is not valid, defaulting to "center"'),Ie(e,F.center)))}(o,t.position),function(e,t){t&&Ie(e,F[`grow-${t}`])}(o,t.grow),ke(o,t,"container"))})(0,t),((e,t)=>{const o=de();if(!o)return;const{progressSteps:n,currentProgressStep:i}=t;n&&0!==n.length&&void 0!==i?(Pe(o),o.textContent="",i>=n.length&&W("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),n.forEach((e,s)=>{const r=(e=>{const t=document.createElement("li");return Ie(t,F["progress-step"]),xe(t,e),t})(e);if(o.appendChild(r),s===i&&Ie(r,F["active-progress-step"]),s!==n.length-1){const e=(e=>{const t=document.createElement("li");return Ie(t,F["progress-step-line"]),e.progressStepsDistance&&De(t,"width",e.progressStepsDistance),t})(t);o.appendChild(e)}})):Be(o)})(0,t),((e,t)=>{const o=We.innerParams.get(e),n=re();if(n){if(o&&t.icon===o.icon)return it(n,t),void ot(n,t);if(t.icon||t.iconHtml)return t.icon&&-1===Object.keys(V).indexOf(t.icon)?(J(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),void Be(n)):(Pe(n),it(n,t),ot(n,t),Ie(n,t.showClass&&t.showClass.icon),void window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",nt));Be(n)}})(e,t),((e,t)=>{const o=ce();o&&(t.imageUrl?(Pe(o,""),o.setAttribute("src",t.imageUrl),o.setAttribute("alt",t.imageAlt||""),De(o,"width",t.imageWidth),De(o,"height",t.imageHeight),o.className=F.image,ke(o,t,"image")):Be(o))})(0,t),((e,t)=>{const o=ae();o&&(Oe(o),Ne(o,t.title||t.titleText,"block"),t.title&&Fe(t.title,o),t.titleText&&(o.innerText=t.titleText),ke(o,t,"title"))})(0,t),((e,t)=>{const o=be();o&&(xe(o,t.closeButtonHtml||""),ke(o,t,"closeButton"),Ne(o,t.showCloseButton),o.setAttribute("aria-label",t.closeButtonAriaLabel||""))})(0,t),tt(e,t),((e,t)=>{const o=fe(),n=we();o&&n&&(t.showConfirmButton||t.showDenyButton||t.showCancelButton?Pe(o):Be(o),ke(o,t,"actions"),function(e,t,o){const n=pe(),i=he(),s=me();n&&i&&s&&(Ue(n,"confirm",o),Ue(i,"deny",o),Ue(s,"cancel",o),function(e,t,o,n){n.buttonsStyling?(Ie([e,t,o],F.styled),n.confirmButtonColor&&(e.style.backgroundColor=n.confirmButtonColor,Ie(e,F["default-outline"])),n.denyButtonColor&&(t.style.backgroundColor=n.denyButtonColor,Ie(t,F["default-outline"])),n.cancelButtonColor&&(o.style.backgroundColor=n.cancelButtonColor,Ie(o,F["default-outline"]))):_e([e,t,o],F.styled)}(n,i,s,o),o.reverseButtons&&(o.toast?(e.insertBefore(s,n),e.insertBefore(i,n)):(e.insertBefore(s,t),e.insertBefore(i,t),e.insertBefore(n,t))))}(o,n,t),xe(n,t.loaderHtml||""),ke(n,t,"loader"))})(0,t),((e,t)=>{const o=ge();o&&(Oe(o),Ne(o,t.footer,"block"),t.footer&&Fe(t.footer,o),ke(o,t,"footer"))})(0,t);const o=se();"function"==typeof t.didRender&&o&&t.didRender(o),z.eventEmitter.emit("didRender",o)},vt=()=>{var e;return null===(e=pe())||void 0===e?void 0:e.click()},bt=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),yt=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},St=(e,t)=>{var o;const n=ye();if(n.length)return(e+=t)===n.length?e=0:-1===e&&(e=n.length-1),void n[e].focus();null===(o=se())||void 0===o||o.focus()},Et=["ArrowRight","ArrowDown"],xt=["ArrowLeft","ArrowUp"],Ct=(e,t)=>{if(!K(t.allowEnterKey))return;const o=Ae(se(),t.input);if(e.target&&o&&e.target instanceof HTMLElement&&e.target.outerHTML===o.outerHTML){if(["textarea","file"].includes(t.input))return;vt(),e.preventDefault()}},kt=e=>{const t=e.target,o=ye();let n=-1;for(let e=0;e<o.length;e++)if(t===o[e]){n=e;break}e.shiftKey?St(n,-1):St(n,1),e.stopPropagation(),e.preventDefault()},At=e=>{const t=fe(),o=pe(),n=he(),i=me();if(!(t&&o&&n&&i))return;const s=[o,n,i];if(document.activeElement instanceof HTMLElement&&!s.includes(document.activeElement))return;const r=Et.includes(e)?"nextElementSibling":"previousElementSibling";let a=document.activeElement;if(a){for(let e=0;e<t.children.length;e++){if(a=a[r],!a)return;if(a instanceof HTMLButtonElement&&qe(a))break}a instanceof HTMLButtonElement&&a.focus()}},Tt=(e,t,o)=>{K(t.allowEscapeKey)&&(e.preventDefault(),o(bt.esc))};var Lt={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const It=()=>{Array.from(document.body.children).forEach(e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")||""),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")})},_t="undefined"!=typeof window&&!!window.GestureEvent,$t=e=>{const t=e.target,o=oe(),n=le();return!(!o||!n||Dt(e)||Pt(e)||t!==o&&(He(o)||!(t instanceof HTMLElement)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||He(n)&&n.contains(t)))},Dt=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Pt=e=>e.touches&&e.touches.length>1;let Bt=null;function Ot(e,t,o,n){Ee()?Rt(e,n):((e=>new Promise(t=>{if(!e)return t();const o=window.scrollX,n=window.scrollY;z.restoreFocusTimeout=setTimeout(()=>{z.previousActiveElement instanceof HTMLElement?(z.previousActiveElement.focus(),z.previousActiveElement=null):document.body&&document.body.focus(),t()},100),window.scrollTo(o,n)}))(o).then(()=>Rt(e,n)),yt(z)),_t?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),Se()&&(null!==Bt&&(document.body.style.paddingRight=`${Bt}px`,Bt=null),(()=>{if(Ce(document.body,F.iosfix)){const e=parseInt(document.body.style.top,10);_e(document.body,F.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}})(),It()),_e([document.documentElement,document.body],[F.shown,F["height-auto"],F["no-backdrop"],F["toast-shown"]])}function Mt(e){e=jt(e);const t=Lt.swalPromiseResolve.get(this),o=Nt(this);this.isAwaitingPromise?e.isDismissed||(Ht(this),t(e)):o&&t(e)}const Nt=e=>{const t=se();if(!t)return!1;const o=We.innerParams.get(e);if(!o||Ce(t,o.hideClass.popup))return!1;_e(t,o.showClass.popup),Ie(t,o.hideClass.popup);const n=oe();return _e(n,o.showClass.backdrop),Ie(n,o.hideClass.backdrop),Xt(e,t,o),!0};function qt(e){const t=Lt.swalPromiseReject.get(this);Ht(this),t&&t(e)}const Ht=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,We.innerParams.get(e)||e._destroy())},jt=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),Xt=(e,t,o)=>{var n;const i=oe(),s=je(t);"function"==typeof o.willClose&&o.willClose(t),null===(n=z.eventEmitter)||void 0===n||n.emit("willClose",t),s?zt(e,t,i,o.returnFocus,o.didClose):Ot(e,i,o.returnFocus,o.didClose)},zt=(e,t,o,n,i)=>{z.swalCloseEventFinishedCallback=Ot.bind(null,e,o,n,i);const s=function(e){var o;e.target===t&&(null===(o=z.swalCloseEventFinishedCallback)||void 0===o||o.call(z),delete z.swalCloseEventFinishedCallback,t.removeEventListener("animationend",s),t.removeEventListener("transitionend",s))};t.addEventListener("animationend",s),t.addEventListener("transitionend",s)},Rt=(e,t)=>{setTimeout(()=>{var o;"function"==typeof t&&t.bind(e.params)(),null===(o=z.eventEmitter)||void 0===o||o.emit("didClose"),e._destroy&&e._destroy()})},Ft=e=>{let t=se();if(t||new an,t=se(),!t)return;const o=we();Ee()?Be(re()):Vt(t,e),Pe(o),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Vt=(e,t)=>{const o=fe(),n=we();o&&n&&(!t&&qe(pe())&&(t=pe()),Pe(o),t&&(Be(t),n.setAttribute("data-button-to-replace",t.className),o.insertBefore(n,t)),Ie([e,o],F.loading))},Yt=e=>{const t=[];return e instanceof Map?e.forEach((e,o)=>{let n=e;"object"==typeof n&&(n=Yt(n)),t.push([o,n])}):Object.keys(e).forEach(o=>{let n=e[o];"object"==typeof n&&(n=Yt(n)),t.push([o,n])}),t},Ut=(e,t)=>!!t&&t.toString()===e.toString(),Wt=(e,t)=>{const o=We.innerParams.get(e);if(!o.input)return void J(`The "input" parameter is needed to be set when using returnInputValueOn${U(t)}`);const n=e.getInput(),i=((e,t)=>{const o=e.getInput();if(!o)return null;switch(t.input){case"checkbox":return(e=>e.checked?1:0)(o);case"radio":return(e=>e.checked?e.value:null)(o);case"file":return(e=>e.files&&e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null)(o);default:return t.inputAutoTrim?o.value.trim():o.value}})(e,o);o.inputValidator?Jt(e,i,t):n&&!n.checkValidity()?(e.enableButtons(),e.showValidationMessage(o.validationMessage||n.validationMessage)):"deny"===t?Gt(e,i):Qt(e,i)},Jt=(e,t,o)=>{const n=We.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>ee(n.inputValidator(t,n.validationMessage))).then(n=>{e.enableButtons(),e.enableInput(),n?e.showValidationMessage(n):"deny"===o?Gt(e,t):Qt(e,t)})},Gt=(e,t)=>{const o=We.innerParams.get(e||void 0);o.showLoaderOnDeny&&Ft(he()),o.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>ee(o.preDeny(t,o.validationMessage))).then(o=>{!1===o?(e.hideLoading(),Ht(e)):e.close({isDenied:!0,value:void 0===o?t:o})}).catch(t=>Kt(e||void 0,t))):e.close({isDenied:!0,value:t})},Zt=(e,t)=>{e.close({isConfirmed:!0,value:t})},Kt=(e,t)=>{e.rejectPromise(t)},Qt=(e,t)=>{const o=We.innerParams.get(e||void 0);o.showLoaderOnConfirm&&Ft(),o.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>ee(o.preConfirm(t,o.validationMessage))).then(o=>{qe(ue())||!1===o?(e.hideLoading(),Ht(e)):Zt(e,void 0===o?t:o)}).catch(t=>Kt(e||void 0,t))):Zt(e,t)};function eo(){const e=We.innerParams.get(this);if(!e)return;const t=We.domCache.get(this);Be(t.loader),Ee()?e.icon&&Pe(re()):to(t),_e([t.popup,t.actions],F.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const to=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Pe(t[0],"inline-block"):qe(pe())||qe(he())||qe(me())||Be(e.actions)};function oo(){const e=We.innerParams.get(this),t=We.domCache.get(this);return t?Ae(t.popup,e.input):null}function no(e,t,o){const n=We.domCache.get(e);t.forEach(e=>{n[e].disabled=o})}function io(e,t){const o=se();if(o&&e)if("radio"===e.type){const e=o.querySelectorAll(`[name="${F.radio}"]`);for(let o=0;o<e.length;o++)e[o].disabled=t}else e.disabled=t}function so(){no(this,["confirmButton","denyButton","cancelButton"],!1)}function ro(){no(this,["confirmButton","denyButton","cancelButton"],!0)}function ao(){io(this.getInput(),!1)}function lo(){io(this.getInput(),!0)}function co(e){const t=We.domCache.get(this),o=We.innerParams.get(this);xe(t.validationMessage,e),t.validationMessage.className=F["validation-message"],o.customClass&&o.customClass.validationMessage&&Ie(t.validationMessage,o.customClass.validationMessage),Pe(t.validationMessage);const n=this.getInput();n&&(n.setAttribute("aria-invalid","true"),n.setAttribute("aria-describedby",F["validation-message"]),Te(n),Ie(n,F.inputerror))}function uo(){const e=We.domCache.get(this);e.validationMessage&&Be(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),_e(t,F.inputerror))}const po={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},mo=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],ho={allowEnterKey:void 0},wo=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],fo=e=>Object.prototype.hasOwnProperty.call(po,e),go=e=>-1!==mo.indexOf(e),vo=e=>ho[e],bo=e=>{fo(e)||W(`Unknown parameter "${e}"`)},yo=e=>{wo.includes(e)&&W(`The parameter "${e}" is incompatible with toasts`)},So=e=>{const t=vo(e);t&&Z(e,t)},Eo=e=>{!1===e.backdrop&&e.allowOutsideClick&&W('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","borderless","embed-iframe"].includes(e.theme)&&W(`Invalid theme "${e.theme}". Expected "light", "dark", "auto", "borderless", or "embed-iframe"`);for(const t in e)bo(t),e.toast&&yo(t),So(t)};function xo(e){const t=oe(),o=se(),n=We.innerParams.get(this);if(!o||Ce(o,n.hideClass.popup))return void W("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const i=Co(e),s=Object.assign({},n,i);Eo(s),t.dataset.swal2Theme=s.theme,gt(this,s),We.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const Co=e=>{const t={};return Object.keys(e).forEach(o=>{go(o)?t[o]=e[o]:W(`Invalid parameter to update: ${o}`)}),t};function ko(){const e=We.domCache.get(this),t=We.innerParams.get(this);t?(e.popup&&z.swalCloseEventFinishedCallback&&(z.swalCloseEventFinishedCallback(),delete z.swalCloseEventFinishedCallback),"function"==typeof t.didDestroy&&t.didDestroy(),z.eventEmitter.emit("didDestroy"),Ao(this)):To(this)}const Ao=e=>{To(e),delete e.params,delete z.keydownHandler,delete z.keydownTarget,delete z.currentInstance},To=e=>{e.isAwaitingPromise?(Lo(We,e),e.isAwaitingPromise=!0):(Lo(Lt,e),Lo(We,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},Lo=(e,t)=>{for(const o in e)e[o].delete(t)};var Io=Object.freeze({__proto__:null,_destroy:ko,close:Mt,closeModal:Mt,closePopup:Mt,closeToast:Mt,disableButtons:ro,disableInput:lo,disableLoading:eo,enableButtons:so,enableInput:ao,getInput:oo,handleAwaitingPromise:Ht,hideLoading:eo,rejectPromise:qt,resetValidationMessage:uo,showValidationMessage:co,update:xo});const _o=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let $o=!1;const Do=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e),Po=()=>{if(z.timeout)return(()=>{const e=ve();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const o=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${o}%`})(),z.timeout.stop()},Bo=()=>{if(z.timeout){const e=z.timeout.start();return Xe(e),e}};let Oo=!1;const Mo={},No=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in Mo){const o=t.getAttribute(e);if(o)return void Mo[e].fire({template:o})}};z.eventEmitter=new class{constructor(){this.events={}}_getHandlersByEventName(e){return void 0===this.events[e]&&(this.events[e]=[]),this.events[e]}on(e,t){const o=this._getHandlersByEventName(e);o.includes(t)||o.push(t)}once(e,t){var o=this;const n=function(){o.removeListener(e,n);for(var i=arguments.length,s=new Array(i),r=0;r<i;r++)s[r]=arguments[r];t.apply(o,s)};this.on(e,n)}emit(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];this._getHandlersByEventName(e).forEach(e=>{try{e.apply(this,o)}catch(e){console.error(e)}})}removeListener(e,t){const o=this._getHandlersByEventName(e),n=o.indexOf(t);n>-1&&o.splice(n,1)}removeAllListeners(e){void 0!==this.events[e]&&(this.events[e].length=0)}reset(){this.events={}}};var qo=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||Do(e[0])?["title","html","icon"].forEach((o,n)=>{const i=e[n];"string"==typeof i||Do(i)?t[o]=i:void 0!==i&&J(`Unexpected type of ${o}! Expected "string" or "Element", got ${typeof i}`)}):Object.assign(t,e[0]),t},bindClickHandler:function(){Mo[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Oo||(document.body.addEventListener("click",No),Oo=!0)},clickCancel:()=>{var e;return null===(e=me())||void 0===e?void 0:e.click()},clickConfirm:vt,clickDeny:()=>{var e;return null===(e=he())||void 0===e?void 0:e.click()},enableLoading:Ft,fire:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new this(...t)},getActions:fe,getCancelButton:me,getCloseButton:be,getConfirmButton:pe,getContainer:oe,getDenyButton:he,getFocusableElements:ye,getFooter:ge,getHtmlContainer:le,getIcon:re,getIconContent:()=>ie(F["icon-content"]),getImage:ce,getInputLabel:()=>ie(F["input-label"]),getLoader:we,getPopup:se,getProgressSteps:de,getTimerLeft:()=>z.timeout&&z.timeout.getTimerLeft(),getTimerProgressBar:ve,getTitle:ae,getValidationMessage:ue,increaseTimer:e=>{if(z.timeout){const t=z.timeout.increase(e);return Xe(t,!0),t}},isDeprecatedParameter:vo,isLoading:()=>{const e=se();return!!e&&e.hasAttribute("data-loading")},isTimerRunning:()=>!(!z.timeout||!z.timeout.isRunning()),isUpdatableParameter:go,isValidParameter:fo,isVisible:()=>qe(se()),mixin:function(e){return class extends(this){_main(t,o){return super._main(t,Object.assign({},e,o))}}},off:(e,t)=>{e?t?z.eventEmitter.removeListener(e,t):z.eventEmitter.removeAllListeners(e):z.eventEmitter.reset()},on:(e,t)=>{z.eventEmitter.on(e,t)},once:(e,t)=>{z.eventEmitter.once(e,t)},resumeTimer:Bo,showLoading:Ft,stopTimer:Po,toggleTimer:()=>{const e=z.timeout;return e&&(e.running?Po():Bo())}});class Ho{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const jo=["swal-title","swal-html","swal-footer"],Xo=(e,t)=>{Array.from(e.attributes).forEach(o=>{-1===t.indexOf(o.name)&&W([`Unrecognized attribute "${o.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])})},zo=e=>{const t=se();if(e.target!==t)return;const o=oe();t.removeEventListener("animationend",zo),t.removeEventListener("transitionend",zo),o.style.overflowY="auto"},Ro=(e,t)=>{je(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",zo),t.addEventListener("transitionend",zo)):e.style.overflowY="auto"},Fo=(e,t,o)=>{(()=>{if(_t&&!Ce(document.body,F.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",Ie(document.body,F.iosfix),(()=>{const e=oe();if(!e)return;let t;e.ontouchstart=e=>{t=$t(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}})()}})(),t&&"hidden"!==o&&(e=>{null===Bt&&(document.body.scrollHeight>window.innerHeight||"scroll"===e)&&(Bt=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Bt+(()=>{const e=document.createElement("div");e.className=F["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)})(o),setTimeout(()=>{e.scrollTop=0})},Vo=(e,t,o)=>{Ie(e,o.showClass.backdrop),o.animation?(t.style.setProperty("opacity","0","important"),Pe(t,"grid"),setTimeout(()=>{Ie(t,o.showClass.popup),t.style.removeProperty("opacity")},10)):Pe(t,"grid"),Ie([document.documentElement,document.body],F.shown),o.heightAuto&&o.backdrop&&!o.toast&&Ie([document.documentElement,document.body],F["height-auto"])};var Yo=(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),Uo=(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL");function Wo(e){!function(e){e.inputValidator||("email"===e.input&&(e.inputValidator=Yo),"url"===e.input&&(e.inputValidator=Uo))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&W("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(W('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),(e=>{const t=(()=>{const e=oe();return!!e&&(e.remove(),_e([document.documentElement,document.body],[F["no-backdrop"],F["toast-shown"],F["has-column"]]),!0)})();if("undefined"==typeof window||"undefined"==typeof document)return void J("SweetAlert2 requires document to initialize");const o=document.createElement("div");o.className=F.container,t&&Ie(o,F["no-transition"]),xe(o,ze),o.dataset.swal2Theme=e.theme;const n="string"==typeof(i=e.target)?document.querySelector(i):i;var i;n.appendChild(o),(e=>{const t=se();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&Ie(oe(),F.rtl)})(n),(()=>{const e=se(),t=$e(e,F.input),o=$e(e,F.file),n=e.querySelector(`.${F.range} input`),i=e.querySelector(`.${F.range} output`),s=$e(e,F.select),r=e.querySelector(`.${F.checkbox} input`),a=$e(e,F.textarea);t.oninput=Re,o.onchange=Re,s.onchange=Re,r.onchange=Re,a.oninput=Re,n.oninput=()=>{Re(),i.value=n.value},n.onchange=()=>{Re(),i.value=n.value}})()})(e)}let Jo;var Go=new WeakMap;class Zo{constructor(){if(function(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}(this,Go,void 0),"undefined"==typeof window)return;Jo=this;for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];const n=Object.freeze(this.constructor.argsToParams(t));var i,s;this.params=n,this.isAwaitingPromise=!1,i=Go,s=this._main(Jo.params),i.set(j(i,this),s)}_main(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Eo(Object.assign({},t,e)),z.currentInstance){const e=Lt.swalPromiseResolve.get(z.currentInstance),{isAwaitingPromise:t}=z.currentInstance;z.currentInstance._destroy(),t||e({isDismissed:!0}),Se()&&It()}z.currentInstance=Jo;const o=Qo(e,t);Wo(o),Object.freeze(o),z.timeout&&(z.timeout.stop(),delete z.timeout),clearTimeout(z.restoreFocusTimeout);const n=en(Jo);return gt(Jo,o),We.innerParams.set(Jo,o),Ko(Jo,n,o)}then(e){return X(Go,this).then(e)}finally(e){return X(Go,this).finally(e)}}const Ko=(e,t,o)=>new Promise((n,i)=>{const s=t=>{e.close({isDismissed:!0,dismiss:t})};Lt.swalPromiseResolve.set(e,n),Lt.swalPromiseReject.set(e,i),t.confirmButton.onclick=()=>{(e=>{const t=We.innerParams.get(e);e.disableButtons(),t.input?Wt(e,"confirm"):Qt(e,!0)})(e)},t.denyButton.onclick=()=>{(e=>{const t=We.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?Wt(e,"deny"):Gt(e,!1)})(e)},t.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(bt.cancel)})(e,s)},t.closeButton.onclick=()=>{s(bt.close)},((e,t,o)=>{e.toast?((e,t,o)=>{t.popup.onclick=()=>{e&&(_o(e)||e.timer||e.input)||o(bt.close)}})(e,t,o):((e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&($o=!0)}}})(t),(e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(t){e.popup.onmouseup=()=>{},(t.target===e.popup||t.target instanceof HTMLElement&&e.popup.contains(t.target))&&($o=!0)}}})(t),((e,t,o)=>{t.container.onclick=n=>{$o?$o=!1:n.target===t.container&&K(e.allowOutsideClick)&&o(bt.backdrop)}})(e,t,o))})(o,t,s),((e,t,o)=>{yt(e),t.toast||(e.keydownHandler=e=>((e,t,o)=>{e&&(t.isComposing||229===t.keyCode||(e.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?Ct(t,e):"Tab"===t.key?kt(t):[...Et,...xt].includes(t.key)?At(t.key):"Escape"===t.key&&Tt(t,e,o)))})(t,e,o),e.keydownTarget=t.keydownListenerCapture?window:se(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)})(z,o,s),((e,t)=>{"select"===t.input||"radio"===t.input?((e,t)=>{const o=se();if(!o)return;const n=e=>{"select"===t.input?function(e,t,o){const n=$e(e,F.select);if(!n)return;const i=(e,t,n)=>{const i=document.createElement("option");i.value=n,xe(i,t),i.selected=Ut(n,o.inputValue),e.appendChild(i)};t.forEach(e=>{const t=e[0],o=e[1];if(Array.isArray(o)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,n.appendChild(e),o.forEach(t=>i(e,t[1],t[0]))}else i(n,o,t)}),n.focus()}(o,Yt(e),t):"radio"===t.input&&function(e,t,o){const n=$e(e,F.radio);if(!n)return;t.forEach(e=>{const t=e[0],i=e[1],s=document.createElement("input"),r=document.createElement("label");s.type="radio",s.name=F.radio,s.value=t,Ut(t,o.inputValue)&&(s.checked=!0);const a=document.createElement("span");xe(a,i),a.className=F.label,r.appendChild(s),r.appendChild(a),n.appendChild(r)});const i=n.querySelectorAll("input");i.length&&i[0].focus()}(o,Yt(e),t)};Q(t.inputOptions)||te(t.inputOptions)?(Ft(pe()),ee(t.inputOptions).then(t=>{e.hideLoading(),n(t)})):"object"==typeof t.inputOptions?n(t.inputOptions):J("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)})(e,t):["text","email","number","tel","textarea"].some(e=>e===t.input)&&(Q(t.inputValue)||te(t.inputValue))&&(Ft(pe()),((e,t)=>{const o=e.getInput();o&&(Be(o),ee(t.inputValue).then(n=>{o.value="number"===t.input?`${parseFloat(n)||0}`:`${n}`,Pe(o),o.focus(),e.hideLoading()}).catch(t=>{J(`Error in inputValue promise: ${t}`),o.value="",Pe(o),o.focus(),e.hideLoading()}))})(e,t))})(e,o),(e=>{const t=oe(),o=se();"function"==typeof e.willOpen&&e.willOpen(o),z.eventEmitter.emit("willOpen",o);const n=window.getComputedStyle(document.body).overflowY;Vo(t,o,e),setTimeout(()=>{Ro(t,o)},10),Se()&&(Fo(t,e.scrollbarPadding,n),(()=>{const e=oe();Array.from(document.body.children).forEach(t=>{t.contains(e)||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")||""),t.setAttribute("aria-hidden","true"))})})()),Ee()||z.previousActiveElement||(z.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout(()=>e.didOpen(o)),z.eventEmitter.emit("didOpen",o),_e(t,F["no-transition"])})(o),tn(z,o,s),on(t,o),setTimeout(()=>{t.container.scrollTop=0})}),Qo=(e,t)=>{const o=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const o=t.content;return(e=>{const t=jo.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(e=>{const o=e.tagName.toLowerCase();t.includes(o)||W(`Unrecognized element <${o}>`)})})(o),Object.assign((e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(e=>{Xo(e,["name","value"]);const o=e.getAttribute("name"),n=e.getAttribute("value");o&&n&&(t[o]="boolean"==typeof po[o]?"false"!==n:"object"==typeof po[o]?JSON.parse(n):n)}),t})(o),(e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(e=>{const o=e.getAttribute("name"),n=e.getAttribute("value");o&&n&&(t[o]=new Function(`return ${n}`)())}),t})(o),(e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(e=>{Xo(e,["type","color","aria-label"]);const o=e.getAttribute("type");o&&["confirm","cancel","deny"].includes(o)&&(t[`${o}ButtonText`]=e.innerHTML,t[`show${U(o)}Button`]=!0,e.hasAttribute("color")&&(t[`${o}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${o}ButtonAriaLabel`]=e.getAttribute("aria-label")))}),t})(o),(e=>{const t={},o=e.querySelector("swal-image");return o&&(Xo(o,["src","width","height","alt"]),o.hasAttribute("src")&&(t.imageUrl=o.getAttribute("src")||void 0),o.hasAttribute("width")&&(t.imageWidth=o.getAttribute("width")||void 0),o.hasAttribute("height")&&(t.imageHeight=o.getAttribute("height")||void 0),o.hasAttribute("alt")&&(t.imageAlt=o.getAttribute("alt")||void 0)),t})(o),(e=>{const t={},o=e.querySelector("swal-icon");return o&&(Xo(o,["type","color"]),o.hasAttribute("type")&&(t.icon=o.getAttribute("type")),o.hasAttribute("color")&&(t.iconColor=o.getAttribute("color")),t.iconHtml=o.innerHTML),t})(o),(e=>{const t={},o=e.querySelector("swal-input");o&&(Xo(o,["type","label","placeholder","value"]),t.input=o.getAttribute("type")||"text",o.hasAttribute("label")&&(t.inputLabel=o.getAttribute("label")),o.hasAttribute("placeholder")&&(t.inputPlaceholder=o.getAttribute("placeholder")),o.hasAttribute("value")&&(t.inputValue=o.getAttribute("value")));const n=Array.from(e.querySelectorAll("swal-input-option"));return n.length&&(t.inputOptions={},n.forEach(e=>{Xo(e,["value"]);const o=e.getAttribute("value");if(!o)return;const n=e.innerHTML;t.inputOptions[o]=n})),t})(o),((e,t)=>{const o={};for(const n in t){const i=t[n],s=e.querySelector(i);s&&(Xo(s,[]),o[i.replace(/^swal-/,"")]=s.innerHTML.trim())}return o})(o,jo))})(e),n=Object.assign({},po,t,o,e);return n.showClass=Object.assign({},po.showClass,n.showClass),n.hideClass=Object.assign({},po.hideClass,n.hideClass),!1===n.animation&&(n.showClass={backdrop:"swal2-noanimation"},n.hideClass={}),n},en=e=>{const t={popup:se(),container:oe(),actions:fe(),confirmButton:pe(),denyButton:he(),cancelButton:me(),loader:we(),closeButton:be(),validationMessage:ue(),progressSteps:de()};return We.domCache.set(e,t),t},tn=(e,t,o)=>{const n=ve();Be(n),t.timer&&(e.timeout=new Ho(()=>{o("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(Pe(n),ke(n,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&Xe(t.timer)})))},on=(e,t)=>{if(!t.toast)return K(t.allowEnterKey)?void(nn(e)||sn(e,t)||St(-1,1)):(Z("allowEnterKey"),void rn())},nn=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const e of t)if(e instanceof HTMLElement&&qe(e))return e.focus(),!0;return!1},sn=(e,t)=>t.focusDeny&&qe(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&qe(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!qe(e.confirmButton)||(e.confirmButton.focus(),0)),rn=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};Zo.prototype.disableButtons=ro,Zo.prototype.enableButtons=so,Zo.prototype.getInput=oo,Zo.prototype.disableInput=lo,Zo.prototype.enableInput=ao,Zo.prototype.hideLoading=eo,Zo.prototype.disableLoading=eo,Zo.prototype.showValidationMessage=co,Zo.prototype.resetValidationMessage=uo,Zo.prototype.close=Mt,Zo.prototype.closePopup=Mt,Zo.prototype.closeModal=Mt,Zo.prototype.closeToast=Mt,Zo.prototype.rejectPromise=qt,Zo.prototype.update=xo,Zo.prototype._destroy=ko,Object.assign(Zo,qo),Object.keys(Io).forEach(e=>{Zo[e]=function(){return Jo&&Jo[e]?Jo[e](...arguments):null}}),Zo.DismissReason=bt,Zo.version="11.18.0";const an=Zo;function ln(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,n)}return o}function cn(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ln(Object(o),!0).forEach(function(t){un(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ln(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function dn(e){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dn(e)}function un(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function pn(){return pn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},pn.apply(this,arguments)}function mn(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}an.default=an,"undefined"!=typeof document&&function(e,t){var o=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(o),o.styleSheet)o.styleSheet.disabled||(o.styleSheet.cssText=t);else try{o.innerHTML=t}catch(e){o.innerText=t}}(document,':root{--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-footer-border-color: #eee;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-background: transparent;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.1s, box-shadow 0.1s;--swal2-close-button-outline: initial;--swal2-close-button-hover-transform: none}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1))}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:box-shadow .1s;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border:0;border-radius:.25em;background:initial;background-color:#7066e0;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):focus-visible{box-shadow:0 0 0 3px rgba(112,102,224,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border:0;border-radius:.25em;background:initial;background-color:#dc3741;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):focus-visible{box-shadow:0 0 0 3px rgba(220,55,65,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border:0;border-radius:.25em;background:initial;background-color:#6e7881;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):focus-visible{box-shadow:0 0 0 3px rgba(110,120,129,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus-visible{box-shadow:0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);color:inherit;font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:border-color .1s,box-shadow .1s;border:1px solid #d9d9d9;border-radius:.1875em;background:var(--swal2-input-background);box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(0,0,0,0);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;background:var(--swal2-background);box-shadow:0 0 1px rgba(0,0,0,.075),0 1px 2px rgba(0,0,0,.075),1px 2px 4px rgba(0,0,0,.075),1px 3px 8px rgba(0,0,0,.075),2px 4px 16px rgba(0,0,0,.075);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}.swal2-toast.swal2-show{animation:swal2-toast-show .5s}.swal2-toast.swal2-hide{animation:swal2-toast-hide .1s forwards}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');var hn=mn(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),wn=mn(/Edge/i),fn=mn(/firefox/i),gn=mn(/safari/i)&&!mn(/chrome/i)&&!mn(/android/i),vn=mn(/iP(ad|od|hone)/i),bn=mn(/chrome/i)&&mn(/android/i),yn={capture:!1,passive:!1};function Sn(e,t,o){e.addEventListener(t,o,!hn&&yn)}function En(e,t,o){e.removeEventListener(t,o,!hn&&yn)}function xn(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(e){return!1}return!1}}function Cn(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function kn(e,t,o,n){if(e){o=o||document;do{if(null!=t&&(">"===t[0]?e.parentNode===o&&xn(e,t):xn(e,t))||n&&e===o)return e;if(e===o)break}while(e=Cn(e))}return null}var An,Tn=/\s+/g;function Ln(e,t,o){if(e&&t)if(e.classList)e.classList[o?"add":"remove"](t);else{var n=(" "+e.className+" ").replace(Tn," ").replace(" "+t+" "," ");e.className=(n+(o?" "+t:"")).replace(Tn," ")}}function In(e,t,o){var n=e&&e.style;if(n){if(void 0===o)return document.defaultView&&document.defaultView.getComputedStyle?o=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(o=e.currentStyle),void 0===t?o:o[t];t in n||-1!==t.indexOf("webkit")||(t="-webkit-"+t),n[t]=o+("string"==typeof o?"":"px")}}function $n(e,t){var o="";if("string"==typeof e)o=e;else do{var n=In(e,"transform");n&&"none"!==n&&(o=n+" "+o)}while(!t&&(e=e.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(o)}function Dn(e,t,o){if(e){var n=e.getElementsByTagName(t),i=0,s=n.length;if(o)for(;i<s;i++)o(n[i],i);return n}return[]}function Pn(){return document.scrollingElement||document.documentElement}function Bn(e,t,o,n,i){if(e.getBoundingClientRect||e===window){var s,r,a,l,c,d,u;if(e!==window&&e.parentNode&&e!==Pn()?(r=(s=e.getBoundingClientRect()).top,a=s.left,l=s.bottom,c=s.right,d=s.height,u=s.width):(r=0,a=0,l=window.innerHeight,c=window.innerWidth,d=window.innerHeight,u=window.innerWidth),(t||o)&&e!==window&&(i=i||e.parentNode,!hn))do{if(i&&i.getBoundingClientRect&&("none"!==In(i,"transform")||o&&"static"!==In(i,"position"))){var p=i.getBoundingClientRect();r-=p.top+parseInt(In(i,"border-top-width")),a-=p.left+parseInt(In(i,"border-left-width")),l=r+s.height,c=a+s.width;break}}while(i=i.parentNode);if(n&&e!==window){var m=$n(i||e),h=m&&m.a,w=m&&m.d;m&&(l=(r/=w)+(d/=w),c=(a/=h)+(u/=h))}return{top:r,left:a,bottom:l,right:c,width:u,height:d}}}function On(e,t,o){for(var n=jn(e,!0),i=Bn(e)[t];n;){var s=Bn(n)[o];if(!("top"===o||"left"===o?i>=s:i<=s))return n;if(n===Pn())break;n=jn(n,!1)}return!1}function Mn(e,t,o,n){for(var i=0,s=0,r=e.children;s<r.length;){if("none"!==r[s].style.display&&r[s]!==Ri.ghost&&(n||r[s]!==Ri.dragged)&&kn(r[s],o.draggable,e,!1)){if(i===t)return r[s];i++}s++}return null}function Nn(e,t){for(var o=e.lastElementChild;o&&(o===Ri.ghost||"none"===In(o,"display")||t&&!xn(o,t));)o=o.previousElementSibling;return o||null}function qn(e,t){var o=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===Ri.clone||t&&!xn(e,t)||o++;return o}function Hn(e){var t=0,o=0,n=Pn();if(e)do{var i=$n(e),s=i.a,r=i.d;t+=e.scrollLeft*s,o+=e.scrollTop*r}while(e!==n&&(e=e.parentNode));return[t,o]}function jn(e,t){if(!e||!e.getBoundingClientRect)return Pn();var o=e,n=!1;do{if(o.clientWidth<o.scrollWidth||o.clientHeight<o.scrollHeight){var i=In(o);if(o.clientWidth<o.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||o.clientHeight<o.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!o.getBoundingClientRect||o===document.body)return Pn();if(n||t)return o;n=!0}}}while(o=o.parentNode);return Pn()}function Xn(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function zn(e,t){return function(){if(!An){var o=arguments;1===o.length?e.call(this,o[0]):e.apply(this,o),An=setTimeout(function(){An=void 0},t)}}}function Rn(e,t,o){e.scrollLeft+=t,e.scrollTop+=o}function Fn(e){var t=window.Polymer,o=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):o?o(e).clone(!0)[0]:e.cloneNode(!0)}function Vn(e,t,o){var n={};return Array.from(e.children).forEach(function(i){var s,r,a,l;if(kn(i,t.draggable,e,!1)&&!i.animated&&i!==o){var c=Bn(i);n.left=Math.min(null!==(s=n.left)&&void 0!==s?s:1/0,c.left),n.top=Math.min(null!==(r=n.top)&&void 0!==r?r:1/0,c.top),n.right=Math.max(null!==(a=n.right)&&void 0!==a?a:-1/0,c.right),n.bottom=Math.max(null!==(l=n.bottom)&&void 0!==l?l:-1/0,c.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var Yn="Sortable"+(new Date).getTime();var Un=[],Wn={initializeByDefault:!0},Jn={mount:function(e){for(var t in Wn)Wn.hasOwnProperty(t)&&!(t in e)&&(e[t]=Wn[t]);Un.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Un.push(e)},pluginEvent:function(e,t,o){var n=this;this.eventCanceled=!1,o.cancel=function(){n.eventCanceled=!0};var i=e+"Global";Un.forEach(function(n){t[n.pluginName]&&(t[n.pluginName][i]&&t[n.pluginName][i](cn({sortable:t},o)),t.options[n.pluginName]&&t[n.pluginName][e]&&t[n.pluginName][e](cn({sortable:t},o)))})},initializePlugins:function(e,t,o,n){for(var i in Un.forEach(function(n){var i=n.pluginName;if(e.options[i]||n.initializeByDefault){var s=new n(e,t,e.options);s.sortable=e,s.options=e.options,e[i]=s,pn(o,s.defaults)}}),e.options)if(e.options.hasOwnProperty(i)){var s=this.modifyOption(e,i,e.options[i]);void 0!==s&&(e.options[i]=s)}},getEventProperties:function(e,t){var o={};return Un.forEach(function(n){"function"==typeof n.eventProperties&&pn(o,n.eventProperties.call(t[n.pluginName],e))}),o},modifyOption:function(e,t,o){var n;return Un.forEach(function(i){e[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[t]&&(n=i.optionListeners[t].call(e[i.pluginName],o))}),n}};var Gn=["evt"],Zn=function(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=o.evt,i=function(e,t){if(null==e)return{};var o,n,i=function(e,t){if(null==e)return{};var o,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)o=s[n],t.indexOf(o)>=0||(i[o]=e[o]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)o=s[n],t.indexOf(o)>=0||Object.prototype.propertyIsEnumerable.call(e,o)&&(i[o]=e[o])}return i}(o,Gn);Jn.pluginEvent.bind(Ri)(e,t,cn({dragEl:Qn,parentEl:ei,ghostEl:ti,rootEl:oi,nextEl:ni,lastDownEl:ii,cloneEl:si,cloneHidden:ri,dragStarted:bi,putSortable:pi,activeSortable:Ri.active,originalEvent:n,oldIndex:ai,oldDraggableIndex:ci,newIndex:li,newDraggableIndex:di,hideGhostForTarget:Hi,unhideGhostForTarget:ji,cloneNowHidden:function(){ri=!0},cloneNowShown:function(){ri=!1},dispatchSortableEvent:function(e){Kn({sortable:t,name:e,originalEvent:n})}},i))};function Kn(e){!function(e){var t=e.sortable,o=e.rootEl,n=e.name,i=e.targetEl,s=e.cloneEl,r=e.toEl,a=e.fromEl,l=e.oldIndex,c=e.newIndex,d=e.oldDraggableIndex,u=e.newDraggableIndex,p=e.originalEvent,m=e.putSortable,h=e.extraEventProperties;if(t=t||o&&o[Yn]){var w,f=t.options,g="on"+n.charAt(0).toUpperCase()+n.substr(1);!window.CustomEvent||hn||wn?(w=document.createEvent("Event")).initEvent(n,!0,!0):w=new CustomEvent(n,{bubbles:!0,cancelable:!0}),w.to=r||o,w.from=a||o,w.item=i||o,w.clone=s,w.oldIndex=l,w.newIndex=c,w.oldDraggableIndex=d,w.newDraggableIndex=u,w.originalEvent=p,w.pullMode=m?m.lastPutMode:void 0;var v=cn(cn({},h),Jn.getEventProperties(n,t));for(var b in v)w[b]=v[b];o&&o.dispatchEvent(w),f[g]&&f[g].call(t,w)}}(cn({putSortable:pi,cloneEl:si,targetEl:Qn,rootEl:oi,oldIndex:ai,oldDraggableIndex:ci,newIndex:li,newDraggableIndex:di},e))}var Qn,ei,ti,oi,ni,ii,si,ri,ai,li,ci,di,ui,pi,mi,hi,wi,fi,gi,vi,bi,yi,Si,Ei,xi,Ci=!1,ki=!1,Ai=[],Ti=!1,Li=!1,Ii=[],_i=!1,$i=[],Di="undefined"!=typeof document,Pi=vn,Bi=wn||hn?"cssFloat":"float",Oi=Di&&!bn&&!vn&&"draggable"in document.createElement("div"),Mi=function(){if(Di){if(hn)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),Ni=function(e,t){var o=In(e),n=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),i=Mn(e,0,t),s=Mn(e,1,t),r=i&&In(i),a=s&&In(s),l=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+Bn(i).width,c=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+Bn(s).width;if("flex"===o.display)return"column"===o.flexDirection||"column-reverse"===o.flexDirection?"vertical":"horizontal";if("grid"===o.display)return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&r.float&&"none"!==r.float){var d="left"===r.float?"left":"right";return!s||"both"!==a.clear&&a.clear!==d?"horizontal":"vertical"}return i&&("block"===r.display||"flex"===r.display||"table"===r.display||"grid"===r.display||l>=n&&"none"===o[Bi]||s&&"none"===o[Bi]&&l+c>n)?"vertical":"horizontal"},qi=function(e){function t(e,o){return function(n,i,s,r){var a=n.options.group.name&&i.options.group.name&&n.options.group.name===i.options.group.name;if(null==e&&(o||a))return!0;if(null==e||!1===e)return!1;if(o&&"clone"===e)return e;if("function"==typeof e)return t(e(n,i,s,r),o)(n,i,s,r);var l=(o?n:i).options.group.name;return!0===e||"string"==typeof e&&e===l||e.join&&e.indexOf(l)>-1}}var o={},n=e.group;n&&"object"==dn(n)||(n={name:n}),o.name=n.name,o.checkPull=t(n.pull,!0),o.checkPut=t(n.put),o.revertClone=n.revertClone,e.group=o},Hi=function(){!Mi&&ti&&In(ti,"display","none")},ji=function(){!Mi&&ti&&In(ti,"display","")};Di&&!bn&&document.addEventListener("click",function(e){if(ki)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),ki=!1,!1},!0);var Xi=function(e){if(Qn){e=e.touches?e.touches[0]:e;var t=(i=e.clientX,s=e.clientY,Ai.some(function(e){var t=e[Yn].options.emptyInsertThreshold;if(t&&!Nn(e)){var o=Bn(e),n=i>=o.left-t&&i<=o.right+t,a=s>=o.top-t&&s<=o.bottom+t;return n&&a?r=e:void 0}}),r);if(t){var o={};for(var n in e)e.hasOwnProperty(n)&&(o[n]=e[n]);o.target=o.rootEl=t,o.preventDefault=void 0,o.stopPropagation=void 0,t[Yn]._onDragOver(o)}}var i,s,r},zi=function(e){Qn&&Qn.parentNode[Yn]._isOutsideThisEl(e.target)};function Ri(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=pn({},t),e[Yn]=this;var o,n,i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ni(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Ri.supportPointer&&"PointerEvent"in window&&(!gn||vn),emptyInsertThreshold:5};for(var s in Jn.initializePlugins(this,e,i),i)!(s in t)&&(t[s]=i[s]);for(var r in qi(t),this)"_"===r.charAt(0)&&"function"==typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!t.forceFallback&&Oi,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Sn(e,"pointerdown",this._onTapStart):(Sn(e,"mousedown",this._onTapStart),Sn(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(Sn(e,"dragover",this),Sn(e,"dragenter",this)),Ai.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),pn(this,(n=[],{captureAnimationState:function(){n=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(e){if("none"!==In(e,"display")&&e!==Ri.ghost){n.push({target:e,rect:Bn(e)});var t=cn({},n[n.length-1].rect);if(e.thisAnimationDuration){var o=$n(e,!0);o&&(t.top-=o.f,t.left-=o.e)}e.fromRect=t}})},addAnimationState:function(e){n.push(e)},removeAnimationState:function(e){n.splice(function(e,t){for(var o in e)if(e.hasOwnProperty(o))for(var n in t)if(t.hasOwnProperty(n)&&t[n]===e[o][n])return Number(o);return-1}(n,{target:e}),1)},animateAll:function(e){var t=this;if(!this.options.animation)return clearTimeout(o),void("function"==typeof e&&e());var i=!1,s=0;n.forEach(function(e){var o=0,n=e.target,r=n.fromRect,a=Bn(n),l=n.prevFromRect,c=n.prevToRect,d=e.rect,u=$n(n,!0);u&&(a.top-=u.f,a.left-=u.e),n.toRect=a,n.thisAnimationDuration&&Xn(l,a)&&!Xn(r,a)&&(d.top-a.top)/(d.left-a.left)===(r.top-a.top)/(r.left-a.left)&&(o=function(e,t,o,n){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-o.top,2)+Math.pow(t.left-o.left,2))*n.animation}(d,l,c,t.options)),Xn(a,r)||(n.prevFromRect=r,n.prevToRect=a,o||(o=t.options.animation),t.animate(n,d,a,o)),o&&(i=!0,s=Math.max(s,o),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout(function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null},o),n.thisAnimationDuration=o)}),clearTimeout(o),i?o=setTimeout(function(){"function"==typeof e&&e()},s):"function"==typeof e&&e(),n=[]},animate:function(e,t,o,n){if(n){In(e,"transition",""),In(e,"transform","");var i=$n(this.el),s=i&&i.a,r=i&&i.d,a=(t.left-o.left)/(s||1),l=(t.top-o.top)/(r||1);e.animatingX=!!a,e.animatingY=!!l,In(e,"transform","translate3d("+a+"px,"+l+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),In(e,"transition","transform "+n+"ms"+(this.options.easing?" "+this.options.easing:"")),In(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout(function(){In(e,"transition",""),In(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},n)}}}))}function Fi(e,t,o,n,i,s,r,a){var l,c,d=e[Yn],u=d.options.onMove;return!window.CustomEvent||hn||wn?(l=document.createEvent("Event")).initEvent("move",!0,!0):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=e,l.dragged=o,l.draggedRect=n,l.related=i||t,l.relatedRect=s||Bn(t),l.willInsertAfter=a,l.originalEvent=r,e.dispatchEvent(l),u&&(c=u.call(d,l,r)),c}function Vi(e){e.draggable=!1}function Yi(){_i=!1}function Ui(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,o=t.length,n=0;o--;)n+=t.charCodeAt(o);return n.toString(36)}function Wi(e){return setTimeout(e,0)}function Ji(e){return clearTimeout(e)}Ri.prototype={constructor:Ri,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(yi=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,Qn):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,o=this.el,n=this.options,i=n.preventOnFilter,s=e.type,r=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,a=(r||e).target,l=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||a,c=n.filter;if(function(e){$i.length=0;for(var t=e.getElementsByTagName("input"),o=t.length;o--;){var n=t[o];n.checked&&$i.push(n)}}(o),!Qn&&!(/mousedown|pointerdown/.test(s)&&0!==e.button||n.disabled)&&!l.isContentEditable&&(this.nativeDraggable||!gn||!a||"SELECT"!==a.tagName.toUpperCase())&&!((a=kn(a,n.draggable,o,!1))&&a.animated||ii===a)){if(ai=qn(a),ci=qn(a,n.draggable),"function"==typeof c){if(c.call(this,e,a,this))return Kn({sortable:t,rootEl:l,name:"filter",targetEl:a,toEl:o,fromEl:o}),Zn("filter",t,{evt:e}),void(i&&e.preventDefault())}else if(c&&(c=c.split(",").some(function(n){if(n=kn(l,n.trim(),o,!1))return Kn({sortable:t,rootEl:n,name:"filter",targetEl:a,fromEl:o,toEl:o}),Zn("filter",t,{evt:e}),!0})))return void(i&&e.preventDefault());n.handle&&!kn(l,n.handle,o,!1)||this._prepareDragStart(e,r,a)}}},_prepareDragStart:function(e,t,o){var n,i=this,s=i.el,r=i.options,a=s.ownerDocument;if(o&&!Qn&&o.parentNode===s){var l=Bn(o);if(oi=s,ei=(Qn=o).parentNode,ni=Qn.nextSibling,ii=o,ui=r.group,Ri.dragged=Qn,mi={target:Qn,clientX:(t||e).clientX,clientY:(t||e).clientY},gi=mi.clientX-l.left,vi=mi.clientY-l.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,Qn.style["will-change"]="all",n=function(){Zn("delayEnded",i,{evt:e}),Ri.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!fn&&i.nativeDraggable&&(Qn.draggable=!0),i._triggerDragStart(e,t),Kn({sortable:i,name:"choose",originalEvent:e}),Ln(Qn,r.chosenClass,!0))},r.ignore.split(",").forEach(function(e){Dn(Qn,e.trim(),Vi)}),Sn(a,"dragover",Xi),Sn(a,"mousemove",Xi),Sn(a,"touchmove",Xi),r.supportPointer?(Sn(a,"pointerup",i._onDrop),!this.nativeDraggable&&Sn(a,"pointercancel",i._onDrop)):(Sn(a,"mouseup",i._onDrop),Sn(a,"touchend",i._onDrop),Sn(a,"touchcancel",i._onDrop)),fn&&this.nativeDraggable&&(this.options.touchStartThreshold=4,Qn.draggable=!0),Zn("delayStart",this,{evt:e}),!r.delay||r.delayOnTouchOnly&&!t||this.nativeDraggable&&(wn||hn))n();else{if(Ri.eventCanceled)return void this._onDrop();r.supportPointer?(Sn(a,"pointerup",i._disableDelayedDrag),Sn(a,"pointercancel",i._disableDelayedDrag)):(Sn(a,"mouseup",i._disableDelayedDrag),Sn(a,"touchend",i._disableDelayedDrag),Sn(a,"touchcancel",i._disableDelayedDrag)),Sn(a,"mousemove",i._delayedDragTouchMoveHandler),Sn(a,"touchmove",i._delayedDragTouchMoveHandler),r.supportPointer&&Sn(a,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(n,r.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){Qn&&Vi(Qn),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;En(e,"mouseup",this._disableDelayedDrag),En(e,"touchend",this._disableDelayedDrag),En(e,"touchcancel",this._disableDelayedDrag),En(e,"pointerup",this._disableDelayedDrag),En(e,"pointercancel",this._disableDelayedDrag),En(e,"mousemove",this._delayedDragTouchMoveHandler),En(e,"touchmove",this._delayedDragTouchMoveHandler),En(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?Sn(document,"pointermove",this._onTouchMove):Sn(document,t?"touchmove":"mousemove",this._onTouchMove):(Sn(Qn,"dragend",this),Sn(oi,"dragstart",this._onDragStart));try{document.selection?Wi(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(e){}},_dragStarted:function(e,t){if(Ci=!1,oi&&Qn){Zn("dragStarted",this,{evt:t}),this.nativeDraggable&&Sn(document,"dragover",zi);var o=this.options;!e&&Ln(Qn,o.dragClass,!1),Ln(Qn,o.ghostClass,!0),Ri.active=this,e&&this._appendGhost(),Kn({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(hi){this._lastX=hi.clientX,this._lastY=hi.clientY,Hi();for(var e=document.elementFromPoint(hi.clientX,hi.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(hi.clientX,hi.clientY))!==t;)t=e;if(Qn.parentNode[Yn]._isOutsideThisEl(e),t)do{if(t[Yn]&&t[Yn]._onDragOver({clientX:hi.clientX,clientY:hi.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break;e=t}while(t=Cn(t));ji()}},_onTouchMove:function(e){if(mi){var t=this.options,o=t.fallbackTolerance,n=t.fallbackOffset,i=e.touches?e.touches[0]:e,s=ti&&$n(ti,!0),r=ti&&s&&s.a,a=ti&&s&&s.d,l=Pi&&xi&&Hn(xi),c=(i.clientX-mi.clientX+n.x)/(r||1)+(l?l[0]-Ii[0]:0)/(r||1),d=(i.clientY-mi.clientY+n.y)/(a||1)+(l?l[1]-Ii[1]:0)/(a||1);if(!Ri.active&&!Ci){if(o&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(ti){s?(s.e+=c-(wi||0),s.f+=d-(fi||0)):s={a:1,b:0,c:0,d:1,e:c,f:d};var u="matrix(".concat(s.a,",").concat(s.b,",").concat(s.c,",").concat(s.d,",").concat(s.e,",").concat(s.f,")");In(ti,"webkitTransform",u),In(ti,"mozTransform",u),In(ti,"msTransform",u),In(ti,"transform",u),wi=c,fi=d,hi=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!ti){var e=this.options.fallbackOnBody?document.body:oi,t=Bn(Qn,!0,Pi,!0,e),o=this.options;if(Pi){for(xi=e;"static"===In(xi,"position")&&"none"===In(xi,"transform")&&xi!==document;)xi=xi.parentNode;xi!==document.body&&xi!==document.documentElement?(xi===document&&(xi=Pn()),t.top+=xi.scrollTop,t.left+=xi.scrollLeft):xi=Pn(),Ii=Hn(xi)}Ln(ti=Qn.cloneNode(!0),o.ghostClass,!1),Ln(ti,o.fallbackClass,!0),Ln(ti,o.dragClass,!0),In(ti,"transition",""),In(ti,"transform",""),In(ti,"box-sizing","border-box"),In(ti,"margin",0),In(ti,"top",t.top),In(ti,"left",t.left),In(ti,"width",t.width),In(ti,"height",t.height),In(ti,"opacity","0.8"),In(ti,"position",Pi?"absolute":"fixed"),In(ti,"zIndex","100000"),In(ti,"pointerEvents","none"),Ri.ghost=ti,e.appendChild(ti),In(ti,"transform-origin",gi/parseInt(ti.style.width)*100+"% "+vi/parseInt(ti.style.height)*100+"%")}},_onDragStart:function(e,t){var o=this,n=e.dataTransfer,i=o.options;Zn("dragStart",this,{evt:e}),Ri.eventCanceled?this._onDrop():(Zn("setupClone",this),Ri.eventCanceled||((si=Fn(Qn)).removeAttribute("id"),si.draggable=!1,si.style["will-change"]="",this._hideClone(),Ln(si,this.options.chosenClass,!1),Ri.clone=si),o.cloneId=Wi(function(){Zn("clone",o),Ri.eventCanceled||(o.options.removeCloneOnHide||oi.insertBefore(si,Qn),o._hideClone(),Kn({sortable:o,name:"clone"}))}),!t&&Ln(Qn,i.dragClass,!0),t?(ki=!0,o._loopId=setInterval(o._emulateDragOver,50)):(En(document,"mouseup",o._onDrop),En(document,"touchend",o._onDrop),En(document,"touchcancel",o._onDrop),n&&(n.effectAllowed="move",i.setData&&i.setData.call(o,n,Qn)),Sn(document,"drop",o),In(Qn,"transform","translateZ(0)")),Ci=!0,o._dragStartId=Wi(o._dragStarted.bind(o,t,e)),Sn(document,"selectstart",o),bi=!0,window.getSelection().removeAllRanges(),gn&&In(document.body,"user-select","none"))},_onDragOver:function(e){var t,o,n,i,s=this.el,r=e.target,a=this.options,l=a.group,c=Ri.active,d=ui===l,u=a.sort,p=pi||c,m=this,h=!1;if(!_i){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),r=kn(r,a.draggable,s,!0),I("dragOver"),Ri.eventCanceled)return h;if(Qn.contains(e.target)||r.animated&&r.animatingX&&r.animatingY||m._ignoreWhileAnimating===r)return $(!1);if(ki=!1,c&&!a.disabled&&(d?u||(n=ei!==oi):pi===this||(this.lastPutMode=ui.checkPull(this,c,Qn,e))&&l.checkPut(this,c,Qn,e))){if(i="vertical"===this._getDirection(e,r),t=Bn(Qn),I("dragOverValid"),Ri.eventCanceled)return h;if(n)return ei=oi,_(),this._hideClone(),I("revert"),Ri.eventCanceled||(ni?oi.insertBefore(Qn,ni):oi.appendChild(Qn)),$(!0);var w=Nn(s,a.draggable);if(!w||function(e,t,o){var n=Bn(Nn(o.el,o.options.draggable)),i=Vn(o.el,o.options,ti);return t?e.clientX>i.right+10||e.clientY>n.bottom&&e.clientX>n.left:e.clientY>i.bottom+10||e.clientX>n.right&&e.clientY>n.top}(e,i,this)&&!w.animated){if(w===Qn)return $(!1);if(w&&s===e.target&&(r=w),r&&(o=Bn(r)),!1!==Fi(oi,s,Qn,t,r,o,e,!!r))return _(),w&&w.nextSibling?s.insertBefore(Qn,w.nextSibling):s.appendChild(Qn),ei=s,D(),$(!0)}else if(w&&function(e,t,o){var n=Bn(Mn(o.el,0,o.options,!0)),i=Vn(o.el,o.options,ti);return t?e.clientX<i.left-10||e.clientY<n.top&&e.clientX<n.right:e.clientY<i.top-10||e.clientY<n.bottom&&e.clientX<n.left}(e,i,this)){var f=Mn(s,0,a,!0);if(f===Qn)return $(!1);if(o=Bn(r=f),!1!==Fi(oi,s,Qn,t,r,o,e,!1))return _(),s.insertBefore(Qn,f),ei=s,D(),$(!0)}else if(r.parentNode===s){o=Bn(r);var g,v,b,y=Qn.parentNode!==s,S=!function(e,t,o){var n=o?e.left:e.top,i=o?e.right:e.bottom,s=o?e.width:e.height,r=o?t.left:t.top,a=o?t.right:t.bottom,l=o?t.width:t.height;return n===r||i===a||n+s/2===r+l/2}(Qn.animated&&Qn.toRect||t,r.animated&&r.toRect||o,i),E=i?"top":"left",x=On(r,"top","top")||On(Qn,"top","top"),C=x?x.scrollTop:void 0;if(yi!==r&&(v=o[E],Ti=!1,Li=!S&&a.invertSwap||y),g=function(e,t,o,n,i,s,r,a){var l=n?e.clientY:e.clientX,c=n?o.height:o.width,d=n?o.top:o.left,u=n?o.bottom:o.right,p=!1;if(!r)if(a&&Ei<c*i){if(!Ti&&(1===Si?l>d+c*s/2:l<u-c*s/2)&&(Ti=!0),Ti)p=!0;else if(1===Si?l<d+Ei:l>u-Ei)return-Si}else if(l>d+c*(1-i)/2&&l<u-c*(1-i)/2)return function(e){return qn(Qn)<qn(e)?1:-1}(t);return(p=p||r)&&(l<d+c*s/2||l>u-c*s/2)?l>d+c/2?1:-1:0}(e,r,o,i,S?1:a.swapThreshold,null==a.invertedSwapThreshold?a.swapThreshold:a.invertedSwapThreshold,Li,yi===r),0!==g){var k=qn(Qn);do{k-=g,b=ei.children[k]}while(b&&("none"===In(b,"display")||b===ti))}if(0===g||b===r)return $(!1);yi=r,Si=g;var A=r.nextElementSibling,T=!1,L=Fi(oi,s,Qn,t,r,o,e,T=1===g);if(!1!==L)return 1!==L&&-1!==L||(T=1===L),_i=!0,setTimeout(Yi,30),_(),T&&!A?s.appendChild(Qn):r.parentNode.insertBefore(Qn,T?A:r),x&&Rn(x,0,C-x.scrollTop),ei=Qn.parentNode,void 0===v||Li||(Ei=Math.abs(v-Bn(r)[E])),D(),$(!0)}if(s.contains(Qn))return $(!1)}return!1}function I(a,l){Zn(a,m,cn({evt:e,isOwner:d,axis:i?"vertical":"horizontal",revert:n,dragRect:t,targetRect:o,canSort:u,fromSortable:p,target:r,completed:$,onMove:function(o,n){return Fi(oi,s,Qn,t,o,Bn(o),e,n)},changed:D},l))}function _(){I("dragOverAnimationCapture"),m.captureAnimationState(),m!==p&&p.captureAnimationState()}function $(t){return I("dragOverCompleted",{insertion:t}),t&&(d?c._hideClone():c._showClone(m),m!==p&&(Ln(Qn,pi?pi.options.ghostClass:c.options.ghostClass,!1),Ln(Qn,a.ghostClass,!0)),pi!==m&&m!==Ri.active?pi=m:m===Ri.active&&pi&&(pi=null),p===m&&(m._ignoreWhileAnimating=r),m.animateAll(function(){I("dragOverAnimationComplete"),m._ignoreWhileAnimating=null}),m!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(r===Qn&&!Qn.animated||r===s&&!r.animated)&&(yi=null),a.dragoverBubble||e.rootEl||r===document||(Qn.parentNode[Yn]._isOutsideThisEl(e.target),!t&&Xi(e)),!a.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),h=!0}function D(){li=qn(Qn),di=qn(Qn,a.draggable),Kn({sortable:m,name:"change",toEl:s,newIndex:li,newDraggableIndex:di,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){En(document,"mousemove",this._onTouchMove),En(document,"touchmove",this._onTouchMove),En(document,"pointermove",this._onTouchMove),En(document,"dragover",Xi),En(document,"mousemove",Xi),En(document,"touchmove",Xi)},_offUpEvents:function(){var e=this.el.ownerDocument;En(e,"mouseup",this._onDrop),En(e,"touchend",this._onDrop),En(e,"pointerup",this._onDrop),En(e,"pointercancel",this._onDrop),En(e,"touchcancel",this._onDrop),En(document,"selectstart",this)},_onDrop:function(e){var t=this.el,o=this.options;li=qn(Qn),di=qn(Qn,o.draggable),Zn("drop",this,{evt:e}),ei=Qn&&Qn.parentNode,li=qn(Qn),di=qn(Qn,o.draggable),Ri.eventCanceled||(Ci=!1,Li=!1,Ti=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Ji(this.cloneId),Ji(this._dragStartId),this.nativeDraggable&&(En(document,"drop",this),En(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),gn&&In(document.body,"user-select",""),In(Qn,"transform",""),e&&(bi&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),ti&&ti.parentNode&&ti.parentNode.removeChild(ti),(oi===ei||pi&&"clone"!==pi.lastPutMode)&&si&&si.parentNode&&si.parentNode.removeChild(si),Qn&&(this.nativeDraggable&&En(Qn,"dragend",this),Vi(Qn),Qn.style["will-change"]="",bi&&!Ci&&Ln(Qn,pi?pi.options.ghostClass:this.options.ghostClass,!1),Ln(Qn,this.options.chosenClass,!1),Kn({sortable:this,name:"unchoose",toEl:ei,newIndex:null,newDraggableIndex:null,originalEvent:e}),oi!==ei?(li>=0&&(Kn({rootEl:ei,name:"add",toEl:ei,fromEl:oi,originalEvent:e}),Kn({sortable:this,name:"remove",toEl:ei,originalEvent:e}),Kn({rootEl:ei,name:"sort",toEl:ei,fromEl:oi,originalEvent:e}),Kn({sortable:this,name:"sort",toEl:ei,originalEvent:e})),pi&&pi.save()):li!==ai&&li>=0&&(Kn({sortable:this,name:"update",toEl:ei,originalEvent:e}),Kn({sortable:this,name:"sort",toEl:ei,originalEvent:e})),Ri.active&&(null!=li&&-1!==li||(li=ai,di=ci),Kn({sortable:this,name:"end",toEl:ei,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){Zn("nulling",this),oi=Qn=ei=ti=ni=si=ii=ri=mi=hi=bi=li=di=ai=ci=yi=Si=pi=ui=Ri.dragged=Ri.ghost=Ri.clone=Ri.active=null,$i.forEach(function(e){e.checked=!0}),$i.length=wi=fi=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":Qn&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],o=this.el.children,n=0,i=o.length,s=this.options;n<i;n++)kn(e=o[n],s.draggable,this.el,!1)&&t.push(e.getAttribute(s.dataIdAttr)||Ui(e));return t},sort:function(e,t){var o={},n=this.el;this.toArray().forEach(function(e,t){var i=n.children[t];kn(i,this.options.draggable,n,!1)&&(o[e]=i)},this),t&&this.captureAnimationState(),e.forEach(function(e){o[e]&&(n.removeChild(o[e]),n.appendChild(o[e]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return kn(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var o=this.options;if(void 0===t)return o[e];var n=Jn.modifyOption(this,e,t);o[e]=void 0!==n?n:t,"group"===e&&qi(o)},destroy:function(){Zn("destroy",this);var e=this.el;e[Yn]=null,En(e,"mousedown",this._onTapStart),En(e,"touchstart",this._onTapStart),En(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(En(e,"dragover",this),En(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ai.splice(Ai.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ri){if(Zn("hideClone",this),Ri.eventCanceled)return;In(si,"display","none"),this.options.removeCloneOnHide&&si.parentNode&&si.parentNode.removeChild(si),ri=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(ri){if(Zn("showClone",this),Ri.eventCanceled)return;Qn.parentNode!=oi||this.options.group.revertClone?ni?oi.insertBefore(si,ni):oi.appendChild(si):oi.insertBefore(si,Qn),this.options.group.revertClone&&this.animate(Qn,si),In(si,"display",""),ri=!1}}else this._hideClone()}},Di&&Sn(document,"touchmove",function(e){(Ri.active||Ci)&&e.cancelable&&e.preventDefault()}),Ri.utils={on:Sn,off:En,css:In,find:Dn,is:function(e,t){return!!kn(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var o in t)t.hasOwnProperty(o)&&(e[o]=t[o]);return e},throttle:zn,closest:kn,toggleClass:Ln,clone:Fn,index:qn,nextTick:Wi,cancelNextTick:Ji,detectDirection:Ni,getChild:Mn,expando:Yn},Ri.get=function(e){return e[Yn]},Ri.mount=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];t[0].constructor===Array&&(t=t[0]),t.forEach(function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Ri.utils=cn(cn({},Ri.utils),e.utils)),Jn.mount(e)})},Ri.create=function(e,t){return new Ri(e,t)},Ri.version="1.15.6";var Gi,Zi,Ki,Qi,es,ts,os=[],ns=!1;function is(){os.forEach(function(e){clearInterval(e.pid)}),os=[]}function ss(){clearInterval(ts)}var rs=zn(function(e,t,o,n){if(t.scroll){var i,s=(e.touches?e.touches[0]:e).clientX,r=(e.touches?e.touches[0]:e).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,c=Pn(),d=!1;Zi!==o&&(Zi=o,is(),Gi=t.scroll,i=t.scrollFn,!0===Gi&&(Gi=jn(o,!0)));var u=0,p=Gi;do{var m=p,h=Bn(m),w=h.top,f=h.bottom,g=h.left,v=h.right,b=h.width,y=h.height,S=void 0,E=void 0,x=m.scrollWidth,C=m.scrollHeight,k=In(m),A=m.scrollLeft,T=m.scrollTop;m===c?(S=b<x&&("auto"===k.overflowX||"scroll"===k.overflowX||"visible"===k.overflowX),E=y<C&&("auto"===k.overflowY||"scroll"===k.overflowY||"visible"===k.overflowY)):(S=b<x&&("auto"===k.overflowX||"scroll"===k.overflowX),E=y<C&&("auto"===k.overflowY||"scroll"===k.overflowY));var L=S&&(Math.abs(v-s)<=a&&A+b<x)-(Math.abs(g-s)<=a&&!!A),I=E&&(Math.abs(f-r)<=a&&T+y<C)-(Math.abs(w-r)<=a&&!!T);if(!os[u])for(var _=0;_<=u;_++)os[_]||(os[_]={});os[u].vx==L&&os[u].vy==I&&os[u].el===m||(os[u].el=m,os[u].vx=L,os[u].vy=I,clearInterval(os[u].pid),0==L&&0==I||(d=!0,os[u].pid=setInterval(function(){n&&0===this.layer&&Ri.active._onTouchMove(es);var t=os[this.layer].vy?os[this.layer].vy*l:0,o=os[this.layer].vx?os[this.layer].vx*l:0;"function"==typeof i&&"continue"!==i.call(Ri.dragged.parentNode[Yn],o,t,e,es,os[this.layer].el)||Rn(os[this.layer].el,o,t)}.bind({layer:u}),24))),u++}while(t.bubbleScroll&&p!==c&&(p=jn(p,!1)));ns=d}},30),as=function(e){var t=e.originalEvent,o=e.putSortable,n=e.dragEl,i=e.activeSortable,s=e.dispatchSortableEvent,r=e.hideGhostForTarget,a=e.unhideGhostForTarget;if(t){var l=o||i;r();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,d=document.elementFromPoint(c.clientX,c.clientY);a(),l&&!l.el.contains(d)&&(s("spill"),this.onSpill({dragEl:n,putSortable:o}))}};function ls(){}function cs(){}ls.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var n=Mn(this.sortable.el,this.startIndex,this.options);n?this.sortable.el.insertBefore(t,n):this.sortable.el.appendChild(t),this.sortable.animateAll(),o&&o.animateAll()},drop:as},pn(ls,{pluginName:"revertOnSpill"}),cs.prototype={onSpill:function(e){var t=e.dragEl,o=e.putSortable||this.sortable;o.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),o.animateAll()},drop:as},pn(cs,{pluginName:"removeOnSpill"}),Ri.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?Sn(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Sn(document,"pointermove",this._handleFallbackAutoScroll):t.touches?Sn(document,"touchmove",this._handleFallbackAutoScroll):Sn(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?En(document,"dragover",this._handleAutoScroll):(En(document,"pointermove",this._handleFallbackAutoScroll),En(document,"touchmove",this._handleFallbackAutoScroll),En(document,"mousemove",this._handleFallbackAutoScroll)),ss(),is(),clearTimeout(An),An=void 0},nulling:function(){es=Zi=Gi=ns=ts=Ki=Qi=null,os.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var o=this,n=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,s=document.elementFromPoint(n,i);if(es=e,t||this.options.forceAutoScrollFallback||wn||hn||gn){rs(e,this.options,s,t);var r=jn(s,!0);!ns||ts&&n===Ki&&i===Qi||(ts&&ss(),ts=setInterval(function(){var s=jn(document.elementFromPoint(n,i),!0);s!==r&&(r=s,is()),rs(e,o.options,s,t)},10),Ki=n,Qi=i)}else{if(!this.options.bubbleScroll||jn(s,!0)===Pn())return void is();rs(e,this.options,jn(s,!1),!1)}}},pn(e,{pluginName:"scroll",initializeByDefault:!0})}),Ri.mount(cs,ls);const ds=Ri,us={...O,elDivAddNewSection:".add-new-section",elSectionClone:".section.clone",elSectionTitleNewInput:".lp-section-title-new-input",elSectionTitleInput:".lp-section-title-input",etBtnEditTitle:".lp-btn-edit-section-title",elSectionDesInput:".lp-section-description-input",elBtnAddSection:".lp-btn-add-section",elBtnUpdateTitle:".lp-btn-update-section-title",elBtnUpdateDes:".lp-btn-update-section-description",elBtnCancelUpdateTitle:".lp-btn-cancel-update-section-title",elBtnCancelUpdateDes:".lp-btn-cancel-update-section-description",elBtnDeleteSection:".lp-btn-delete-section",elSectionDesc:".section-description",elSectionToggle:".section-toggle",elCountSections:".count-sections"};let{$3:ps,$g:ms,hV:hs,P0:ws,EO:fs,P9:gs}=t;const vs="edit-course-curriculum",bs=()=>{({$3:ps,$g:ms,hV:hs,P0:ws,EO:fs,P9:gs}=t)},ys=(e,t,o=!0)=>{const n=t.closest(`${us.elSectionTitleNewInput}`);if(!n)return;const i=n.closest(`${us.elDivAddNewSection}`);i&&(o?i.classList.add("focus"):i.classList.remove("focus"))},Ss=(e,t)=>{let o=!1;if((t.closest(`${us.elBtnAddSection}`)||t.closest(`${us.elSectionTitleNewInput}`)&&"Enter"===e.key)&&(o=!0),!o)return;const n=t.closest(`${us.elDivAddNewSection}`);if(!n)return;e.preventDefault();const i=n.querySelector(`${us.elSectionTitleNewInput}`),s=i.value.trim(),r=i.dataset.messEmptyTitle;if(0===s.length)return void ws(r,"error");i.value="",i.blur();const a=hs.querySelector(`${us.elSectionClone}`).cloneNode(!0);a.classList.remove("clone"),fs.lpShowHideEl(a,1),fs.lpSetLoadingEl(a,1),a.querySelector(`${us.elSectionTitleInput}`).value=s,hs.insertAdjacentElement("beforeend",a);const l={success:e=>{const{message:t,status:o,data:n}=e;if("error"===o)a.remove();else if("success"===o){const{section:e}=n;a.dataset.sectionId=e.section_id||"",P&&P()}ws(t,o)},error:e=>{a.remove(),ws(e,"error")},completed:()=>{fs.lpSetLoadingEl(a,0),a.classList.remove(`${us.elCollapse}`),a.querySelector(`${us.elSectionDesInput}`).focus(),ks(),delete B.titleNew}},c={action:"add_section",course_id:ps,section_name:s,args:{id_url:vs}};window.lpAJAXG.fetchAJAX(c,l)},Es=(e,t,o=!0)=>{const n=t.closest(`${us.elSectionTitleInput}`);if(!n)return;const i=n.closest(`${us.elSection}`);i&&(o?i.classList.add("focus"):i.classList.remove("focus"))},xs=(e,t)=>{let o=!1;if((t.closest(`${us.elBtnUpdateTitle}`)||t.closest(`${us.elSectionTitleInput}`)&&"Enter"===e.key)&&(o=!0),!o)return;e.preventDefault();const n=t.closest(`${us.elSection}`);if(!n)return;const i=n.querySelector(`${us.elSectionTitleInput}`);if(!i)return;const s=n.dataset.sectionId,r=i.value.trim(),a=i.dataset.old||"",l=i.dataset.messEmptyTitle;if(0===r.length)return void ws(l,"error");if(r===a)return;i.blur(),fs.lpSetLoadingEl(n,1);const c={success:e=>{const{message:t,status:o}=e;ws(t,o),"success"===o&&(i.dataset.old=r)},error:e=>{ws(e,"error")},completed:()=>{fs.lpSetLoadingEl(n,0),n.classList.remove("editing"),delete B.title}},d={action:"update_section",course_id:ps,section_id:s,section_name:r,args:{id_url:vs}};window.lpAJAXG.fetchAJAX(d,c)},Cs=(e,t)=>{let o=!1;if((t.closest(`${us.elBtnUpdateDes}`)||t.closest(`${us.elSectionDesInput}`)&&"Enter"===e.key)&&(o=!0),!o)return;e.preventDefault();const n=t.closest(`${us.elSectionDesc}`);if(!n)return;const i=n.querySelector(`${us.elSectionDesInput}`);if(!i)return;const s=i.closest(`${us.elSection}`),r=s.dataset.sectionId,a=i.value.trim(),l=i.dataset.old||"";if(a===l)return;fs.lpSetLoadingEl(s,1);const c={success:e=>{const{message:t,status:o}=e;ws(t,o)},error:e=>{ws(e,"error")},completed:()=>{fs.lpSetLoadingEl(s,0),i.closest(`${us.elSectionDesc}`).classList.remove("editing"),i.dataset.old=a}},d={action:"update_section",course_id:ps,section_id:r,section_description:a,args:{id_url:vs}};window.lpAJAXG.fetchAJAX(d,c)},ks=()=>{const e=ms.querySelector(`${us.elCountSections}`),t=hs.querySelectorAll(`${us.elSection}:not(.clone)`).length;e.dataset.count=t,e.querySelector(".count").textContent=t},As={...O,elSectionListItems:".section-list-items",elItemClone:".section-item.clone",elSectionItem:".section-item",elBtnSelectItemType:".lp-btn-select-item-type",elAddItemTypeClone:".lp-add-item-type.clone",elSectionActions:".section-actions",elAddItemType:".lp-add-item-type",elAddItemTypeTitleInput:".lp-add-item-type-title-input",elBtnAddItemCancel:".lp-btn-add-item-cancel",elBtnAddItem:".lp-btn-add-item",elItemTitleInput:".lp-item-title-input",elBtnUpdateItemTitle:".lp-btn-update-item-title",elBtnCancelUpdateTitle:".lp-btn-cancel-update-item-title",elBtnDeleteItem:".lp-btn-delete-item",elBtnShowPopupItemsToSelect:".lp-btn-show-popup-items-to-select",elPopupItemsToSelectClone:".lp-popup-items-to-select.clone",elPopupItemsToSelect:".lp-popup-items-to-select",elSelectItem:".lp-select-item",elListItemsWrap:".list-items-wrap",elListItems:".list-items",elBtnAddItemsSelected:".lp-btn-add-items-selected",elBtnCountItemsSelected:".lp-btn-count-items-selected",elBtnBackListItems:".lp-btn-back-to-select-items",elHeaderCountItemSelected:".header-count-items-selected",elListItemsSelected:".list-items-selected",elItemSelectedClone:".li-item-selected.clone",elItemSelected:".li-item-selected",elBtnSetPreviewItem:".lp-btn-set-preview-item"};let{$3:Ts,hV:Ls,P0:Is,EO:_s,P9:$s}=t;const Ds="edit-course-curriculum",Ps=(e,t)=>{let o=!1;if((t.closest(`${As.elBtnAddItem}`)||t.closest(`${As.elAddItemTypeTitleInput}`)&&"Enter"===e.key)&&(o=!0),!o)return;e.preventDefault();const n=t.closest(`${As.elAddItemType}`),i=n.closest(`${As.elSection}`),s=i.dataset.sectionId,r=n.querySelector(`${As.elAddItemTypeTitleInput}`),a=r.value.trim(),l=r.dataset.itemType,c=r.dataset.messEmptyTitle;if(0===a.length)return void Is(c,"error");const d=i.querySelector(`${As.elItemClone}`),u=d.cloneNode(!0),p=u.querySelector(`${As.elItemTitleInput}`);u.classList.remove("clone"),u.classList.add(l),u.dataset.itemType=l,_s.lpShowHideEl(u,1),_s.lpSetLoadingEl(u,1),p.value=a,p.dataset.old=a,d.insertAdjacentElement("beforebegin",u),n.remove();const m={success:e=>{const{message:t,status:o,data:n}=e;if(Is(t,o),"error"===o)u.remove();else if("success"===o){const{section_item:e,item_link:t}=n||{};u.dataset.itemId=e.item_id||0,u.querySelector(".edit-link").setAttribute("href",t||"")}},error:e=>{Is(e,"error"),u.remove()},completed:()=>{_s.lpSetLoadingEl(u,0),$s(i)}},h={course_id:Ts,action:"create_item_add_to_section",section_id:s,item_title:a,item_type:l,args:{id_url:Ds}};window.lpAJAXG.fetchAJAX(h,m)},Bs=(e,t,o=!0)=>{const n=t.closest(`${As.elItemTitleInput}`);if(!n)return;const i=n.closest(`${As.elSectionItem}`);i&&(o?i.classList.add("focus"):i.classList.remove("focus"))},Os=(e,t)=>{let o=!1;if((t.closest(`${As.elBtnUpdateItemTitle}`)||t.closest(`${As.elItemTitleInput}`)&&"Enter"===e.key)&&(o=!0),!o)return;e.preventDefault();const n=t.closest(`${As.elSectionItem}`);if(!n)return;const i=n.closest(`${As.elSection}`);if(!i)return;const s=n.querySelector(`${As.elItemTitleInput}`);if(!s)return;const r=n.dataset.itemId,a=n.dataset.itemType,l=s.value.trim(),c=s.dataset.old,d=s.dataset.messEmptyTitle;if(0===l.length)return void Is(d,"error");if(l===c)return;s.blur(),_s.lpSetLoadingEl(n,1);const u={success:e=>{const{message:t,status:o}=e;"success"===o?s.dataset.old=l:s.value=c,Is(t,o)},error:e=>{Is(e,"error")},completed:()=>{_s.lpSetLoadingEl(n,0),n.classList.remove("editing")}},p={course_id:Ts,action:"update_item_of_section",section_id:i.dataset.sectionId,item_id:r,item_type:a,item_title:l,args:{id_url:Ds}};window.lpAJAXG.fetchAJAX(p,u)},Ms=()=>{const e=Ls.querySelectorAll(`${As.elSectionListItems}`);let t,o=0,n=0,i=0;e.forEach(e=>{new ds(e,{handle:".drag",animation:150,group:{name:"shared"},onEnd:e=>{const s=[],r=e.item;i=r.closest(`${As.elSection}`).dataset.sectionId;const a={course_id:Ts,args:{id_url:Ds}};n===i?(a.action="update_items_position",a.section_id=i):(a.action="update_item_section_and_position",a.item_id_change=o,a.section_id_new_of_item=i,a.section_id_old_of_item=n);const l=Ls.querySelector(`.section[data-section-id="${i}"]`);l.querySelectorAll(`${As.elSectionItem}`).forEach(e=>{const t=parseInt(e.dataset.itemId||0);0!==t&&s.push(t)}),a.items_position=s;const c={success:e=>{const{message:t,status:o}=e;Is(t,o)},error:e=>{Is(e,"error")},completed:()=>{_s.lpSetLoadingEl(r,0),$s(l),n!==i&&$s(t)}};_s.lpSetLoadingEl(r,1),window.lpAJAXG.fetchAJAX(a,c)},onMove:e=>{},onChoose:e=>{const i=e.item;o=i.dataset.itemId,t=i.closest(`${As.elSection}`),n=t.dataset.sectionId},onUpdate:e=>{}})})};let Ns,qs,Hs,js=[];const Xs=()=>{if(!qs)return;const e=qs.querySelector(`${As.elBtnAddItemsSelected}`),t=qs.querySelector(`${As.elBtnCountItemsSelected}`),o=t.querySelector("span"),n=qs.querySelector(`${As.elHeaderCountItemSelected}`);0!==js.length?(t.disabled=!1,e.disabled=!1,o.textContent=`(${js.length})`,n.innerHTML=t.innerHTML):(t.disabled=!0,e.disabled=!0,o.textContent="",n.textContent=""),qs.querySelector(`${As.elListItems}`).querySelectorAll('input[type="checkbox"]').forEach(e=>{const t=e.value,o=(e.dataset.type,e.dataset.title,js.some(e=>e.item_id===t));e.checked=o})},{s7:zs}=t,Rs=e=>{const t=_,o=t.querySelector(".total-items"),n=t.querySelectorAll(`${zs.elSectionItem}:not(.clone)`).length;o.dataset.count=n,o.querySelector(".count").textContent=n;const i=e.querySelector(".section-items-counts"),s=e.querySelectorAll(`${zs.elSectionItem}:not(.clone)`).length;i.dataset.count=s,i.querySelector(".count").textContent=s};document.addEventListener("click",e=>{const t=e.target;Ss(e,t),((e,t)=>{const o=t.closest(`${us.etBtnEditTitle}`);if(!o)return;const n=o.closest(`${us.elSection}`);if(!n)return;const i=n.querySelector(`${us.elSectionTitleInput}`);i.setSelectionRange(i.value.length,i.value.length),i.focus()})(0,t),((e,t)=>{const o=t.closest(`${us.elSectionToggle}`);if(!o)return;const n=o.closest(`${us.elSection}`);n.closest(`${us.elCurriculumSections}`)&&(n.classList.toggle(`${us.elCollapse}`),(()=>{const e=ms.querySelectorAll(`${us.elSection}:not(.clone)`),t=ms.querySelector(`${us.elToggleAllSections}`);let o=!0;e.forEach(e=>{if(e.classList.contains(`${us.elCollapse}`))return o=!1,!1}),o?t.classList.remove(`${us.elCollapse}`):t.classList.add(`${us.elCollapse}`)})())})(0,t),Cs(e,t),((e,t)=>{const o=t.closest(`${us.elBtnCancelUpdateDes}`);if(!o)return;const n=o.closest(`${us.elSectionDesc}`),i=n.querySelector(`${us.elSectionDesInput}`);i.value=i.dataset.old||"",n.classList.remove("editing")})(0,t),((e,t)=>{const o=t.closest(`${us.elBtnDeleteSection}`);o&&an.fire({title:o.dataset.title,text:o.dataset.content,icon:"warning",showCloseButton:!0,showCancelButton:!0,cancelButtonText:lpDataAdmin.i18n.cancel,confirmButtonText:lpDataAdmin.i18n.yes,reverseButtons:!0}).then(e=>{if(e.isConfirmed){const e=o.closest(".section"),t=e.dataset.sectionId;fs.lpSetLoadingEl(e,1);const n={success:e=>{const{message:t,status:o}=e,{content:n}=e.data;ws(t,o)},error:e=>{ws(e,"error")},completed:()=>{fs.lpSetLoadingEl(e,0),e.remove(),gs(e),ks()}},i={action:"delete_section",course_id:ps,section_id:t,args:{id_url:vs}};window.lpAJAXG.fetchAJAX(i,n)}})})(0,t),xs(e,t),((e,t)=>{const o=t.closest(`${us.elBtnCancelUpdateTitle}`);if(!o)return;const n=o.closest(`${us.elSection}`),i=n.querySelector(`${us.elSectionTitleInput}`);i.value=i.dataset.old||"",n.classList.remove("editing"),delete B.title})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnSelectItemType}`);if(!o)return;const n=o.dataset.itemType,i=o.dataset.placeholder,s=o.dataset.buttonAddText,r=o.closest(`${As.elSection}`).querySelector(`${As.elSectionActions}`),a=r.querySelector(`${As.elAddItemTypeClone}`).cloneNode(!0),l=a.querySelector(`${As.elAddItemTypeTitleInput}`),c=a.querySelector(`${As.elBtnAddItem}`);a.classList.remove("clone"),a.classList.add(n),_s.lpShowHideEl(a,1),l.setAttribute("placeholder",i),l.dataset.itemType=n,c.textContent=s,r.insertAdjacentElement("beforebegin",a),l.focus()})(0,t),((e,t)=>{if(!t.closest(`${As.elBtnAddItemCancel}`))return;const o=t.closest(`${As.elAddItemType}`);o&&o.remove()})(0,t),Ps(e,t),((e,t)=>{const o=t.closest(`${As.elBtnShowPopupItemsToSelect}`);if(!o)return;const n=o.closest(`${As.elSection}`);Ns=n.dataset.sectionId;const i=document.querySelector(`${As.elPopupItemsToSelectClone}`);qs=i.cloneNode(!0),qs.classList.remove("clone"),_s.lpShowHideEl(qs,1),an.fire({html:qs,showConfirmButton:!1,showCloseButton:!0,width:"60%",customClass:{popup:"lp-select-items-popup",htmlContainer:"lp-select-items-html-container",container:"lp-select-items-container"},willOpen:()=>{qs.querySelector('li[data-type="lp_lesson"]').click()}}).then(e=>{e.isDismissed})})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnCountItemsSelected}`);if(!o)return;const n=o.closest(`${As.elPopupItemsToSelect}`);if(!n)return;const i=n.querySelector(`${As.elBtnBackListItems}`),s=n.querySelector(".tabs"),r=n.querySelector(`${As.elListItemsWrap}`),a=n.querySelector(`${As.elHeaderCountItemSelected}`),l=n.querySelector(`${As.elListItemsSelected}`),c=l.querySelector(`${As.elItemSelectedClone}`);a.innerHTML=o.innerHTML,_s.lpShowHideEl(r,0),_s.lpShowHideEl(o,0),_s.lpShowHideEl(s,0),_s.lpShowHideEl(i,1),_s.lpShowHideEl(a,1),_s.lpShowHideEl(l,1),l.querySelectorAll(`${As.elItemSelected}:not(.clone)`).forEach(e=>{e.remove()}),js.forEach(e=>{const t=c.cloneNode(!0);t.classList.remove("clone"),t.dataset.id=e.item_id,t.dataset.type=e.item_type||"",t.querySelector(".item-title").textContent=e.item_title||"",t.querySelector(".item-id").textContent=e.item_id||"",t.querySelector(".item-type").textContent=e.item_type||"",_s.lpShowHideEl(t,1),c.insertAdjacentElement("beforebegin",t)})})(0,t),((e,t)=>{const o=t.closest(".tab");if(!o)return;e.preventDefault();const n=o.closest(".tabs");if(!n)return;const i=n.closest(`${As.elPopupItemsToSelect}`),s=i.querySelector(".lp-search-title-item"),r=o.dataset.type;n.querySelectorAll(".tab").forEach(e=>{e.classList.contains("active")&&e.classList.remove("active")}),o.classList.add("active"),s.value="";const a=i.querySelector(`${As.LPTarget}`),l=window.lpAJAXG.getDataSetCurrent(a);l.args.item_type=r,l.args.paged=1,l.args.item_selecting=js||[],window.lpAJAXG.setDataSetCurrent(a,l),window.lpAJAXG.showHideLoading(a,1),window.lpAJAXG.fetchAJAX(l,{success:e=>{const{data:t}=e;a.innerHTML=t.content||""},error:e=>{Is(e,"error")},completed:()=>{window.lpAJAXG.showHideLoading(a,0),Xs()}})})(e,t),Os(e,t),((e,t)=>{const o=t.closest(`${As.elBtnCancelUpdateTitle}`);if(!o)return;const n=o.closest(`${As.elSectionItem}`),i=n.querySelector(`${As.elItemTitleInput}`);i.value=i.dataset.old||"",n.classList.remove("editing")})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnDeleteItem}`);if(!o)return;const n=o.closest(`${As.elSectionItem}`);if(!n)return;const i=n.dataset.itemId,s=n.closest(`${As.elSection}`),r=s.dataset.sectionId;an.fire({title:o.dataset.title,text:o.dataset.content,icon:"warning",showCloseButton:!0,showCancelButton:!0,cancelButtonText:lpDataAdmin.i18n.cancel,confirmButtonText:lpDataAdmin.i18n.yes,reverseButtons:!0}).then(e=>{if(e.isConfirmed){_s.lpSetLoadingEl(n,1);const e={success:e=>{const{message:t,status:o}=e;Is(t,o),"success"===o&&n.remove()},error:e=>{Is(e,"error")},completed:()=>{_s.lpSetLoadingEl(n,0),$s(s)}},t={course_id:Ts,action:"delete_item_from_section",section_id:r,item_id:i,args:{id_url:Ds}};window.lpAJAXG.fetchAJAX(t,e)}})})(0,t),((e,t)=>{const o=t.closest(`${As.elSelectItem}`);if(!o)return;const n=o.querySelector('input[type="checkbox"]');if("INPUT"!==t.tagName)return void n.click();if(!o.closest(`${As.elListItems}`))return;const i={item_id:n.value,item_type:n.dataset.type||"",item_title:n.dataset.title||"",item_edit_link:n.dataset.editLink||""};if(n.checked)js.some(e=>e.item_id===i.item_id)||js.push(i);else{const e=js.findIndex(e=>e.item_id===i.item_id);-1!==e&&js.splice(e,1)}Xs()})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnAddItemsSelected}`);if(!o)return;if(!o.closest(`${As.elPopupItemsToSelect}`))return;const n=document.querySelector(`.section[data-section-id="${Ns}"]`),i=n.querySelector(`${As.elItemClone}`);js.forEach(e=>{const t=i.cloneNode(!0),o=t.querySelector(`${As.elItemTitleInput}`);t.dataset.itemId=e.item_id,t.classList.add(e.item_type),t.classList.remove("clone"),t.dataset.itemType=e.item_type,t.querySelector(".edit-link").setAttribute("href",e.item_edit_link||""),o.value=e.item_title||"",_s.lpSetLoadingEl(t,1),_s.lpShowHideEl(t,1),i.insertAdjacentElement("beforebegin",t)}),an.close();const s={course_id:Ts,action:"add_items_to_section",section_id:Ns,items:js,args:{id_url:Ds}};window.lpAJAXG.fetchAJAX(s,{success:e=>{const{message:t,status:o}=e;Is(t,o),"error"===o&&js.forEach(e=>{const t=n.querySelector(`${As.elSectionItem}[data-item-id="${e.item_id}"]`);t&&t.remove()})},error:e=>{Is(e,"error")},completed:()=>{js.forEach(e=>{const t=n.querySelector(`${As.elSectionItem}[data-item-id="${e.item_id}"]`);_s.lpSetLoadingEl(t,0)}),js=[],$s(n)}})})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnBackListItems}`);if(!o)return;const n=o.closest(`${As.elPopupItemsToSelect}`),i=n.querySelector(`${As.elBtnCountItemsSelected}`),s=n.querySelector(".tabs"),r=n.querySelector(`${As.elListItemsWrap}`),a=n.querySelector(`${As.elHeaderCountItemSelected}`),l=n.querySelector(`${As.elListItemsSelected}`);_s.lpShowHideEl(i,1),_s.lpShowHideEl(r,1),_s.lpShowHideEl(s,1),_s.lpShowHideEl(o,0),_s.lpShowHideEl(a,0),_s.lpShowHideEl(l,0)})(0,t),((e,t)=>{const o=t.closest(`${As.elItemSelected}`);if(!o)return;const n=o.dataset.id,i=(o.dataset.type,js.findIndex(e=>e.item_id===n));-1!==i&&js.splice(i,1),o.remove(),Xs()})(0,t),((e,t)=>{const o=t.closest(`${As.elBtnSetPreviewItem}`);if(!o)return;const n=o.closest(`${As.elSectionItem}`);if(!n)return;const i=o.querySelector("a");i.classList.toggle("lp-icon-eye"),i.classList.toggle("lp-icon-eye-slash");const s=!i.classList.contains("lp-icon-eye-slash"),r=n.dataset.itemId,a=n.dataset.itemType;_s.lpSetLoadingEl(n,1);const l={success:e=>{const{message:t,status:o}=e;Is(t,o),"error"===o&&(i.classList.toggle("lp-icon-eye"),i.classList.toggle("lp-icon-eye-slash"))},error:e=>{Is(e,"error"),i.classList.toggle("lp-icon-eye"),i.classList.toggle("lp-icon-eye-slash")},completed:()=>{_s.lpSetLoadingEl(n,0)}},c={course_id:Ts,action:"update_item_preview",item_id:r,item_type:a,enable_preview:s?1:0,args:{id_url:Ds}};window.lpAJAXG.fetchAJAX(c,l)})(0,t),((e,t)=>{const o=t.closest(`${zs.elToggleAllSections}`);if(!o)return;const n=_.querySelectorAll(`${zs.elSection}:not(.clone)`);o.classList.toggle(`${zs.elCollapse}`),o.classList.contains(`${zs.elCollapse}`)?n.forEach(e=>{e.classList.contains(`${zs.elCollapse}`)||e.classList.add(`${zs.elCollapse}`)}):n.forEach(e=>{e.classList.contains(`${zs.elCollapse}`)&&e.classList.remove(`${zs.elCollapse}`)})})(0,t)}),document.addEventListener("keydown",e=>{const t=e.target;"Enter"===e.key&&(Ss(e,t),xs(e,t),Cs(e,t),Ps(e,t),Os(e,t))}),document.addEventListener("keyup",e=>{const t=e.target;((e,t)=>{const o=t.closest(`${us.elSectionTitleNewInput}`);if(!o)return;const n=o.closest(`${us.elDivAddNewSection}`);if(!n)return;const i=n.querySelector(`${us.elBtnAddSection}`);0===o.value.trim().length?(i.classList.remove("active"),delete B.titleNew):(i.classList.add("active"),B.titleNew=1)})(0,t),((e,t)=>{const o=t.closest(`${us.elSectionTitleInput}`);if(!o)return;const n=o.closest(`${us.elSection}`);o.value.trim()===(o.dataset.old||"")?(n.classList.remove("editing"),delete B.title):(n.classList.add("editing"),B.title=1)})(0,t),((e,t)=>{const o=t.closest(`${us.elSectionDesInput}`);if(!o)return;const n=o.closest(`${us.elSectionDesc}`);o.value.trim()===(o.dataset.old||"")?n.classList.remove("editing"):n.classList.add("editing")})(0,t),((e,t)=>{const o=t.closest(`${As.elItemTitleInput}`);if(!o)return;const n=o.closest(`${As.elSectionItem}`);n&&(o.value.trim()===(o.dataset.old||"")?n.classList.remove("editing"):n.classList.add("editing"))})(0,t),((e,t)=>{const o=t.closest(`${As.elAddItemTypeTitleInput}`);if(!o)return;const n=o.closest(`${As.elAddItemType}`);if(!n)return;const i=n.querySelector(`${As.elBtnAddItem}`);i&&(0===o.value.trim().length?i.classList.remove("active"):i.classList.add("active"))})(0,t),((e,t)=>{const o=t.closest(".lp-search-title-item");if(!o)return;const n=o.closest(`${As.elPopupItemsToSelect}`);if(!n)return;const i=n.querySelector(`${As.LPTarget}`);clearTimeout(Hs),Hs=setTimeout(()=>{const e=window.lpAJAXG.getDataSetCurrent(i);e.args.search_title=o.value.trim(),e.args.item_selecting=js,window.lpAJAXG.showHideLoading(i,1),window.lpAJAXG.fetchAJAX(e,{success:e=>{const{data:t}=e;i.innerHTML=t.content||""},error:e=>{Is(e,"error")},completed:()=>{window.lpAJAXG.showHideLoading(i,0)}})},1e3)})(0,t)}),document.addEventListener("focusin",e=>{ys(0,e.target),Es(0,e.target),Bs(0,e.target)}),document.addEventListener("focusout",e=>{ys(0,e.target,!1),Es(0,e.target,!1),Bs(0,e.target,!1)}),window.addEventListener("beforeunload",function(e){0!==Object.keys(B).length&&(e.preventDefault(),e.returnValue="")}),c(`${zs.idElEditCurriculum}`,e=>{const o=e.querySelector(`${zs.elCurriculumSections}`),n=e.closest(`${zs.LPTarget}`),i=window.lpAJAXG.getDataSetCurrent(n);q({courseId:i.args.course_id,elEditCurriculum:e,elCurriculumSections:o,elLPTarget:n,updateCountItems:Rs,hasChange:{}}),bs(),(()=>{let e,t=0;new ds(hs,{handle:".drag",animation:150,onEnd:o=>{const n=o.item;if(!t)return;const i=n.closest(`${us.elSection}`),s=hs.querySelectorAll(`${us.elSection}`),r=[];s.forEach((e,t)=>{const o=e.dataset.sectionId;r.push(o)});const a={success:e=>{const{message:t,status:o}=e;ws(t,o)},error:e=>{ws(e,"error")},completed:()=>{fs.lpSetLoadingEl(i,0),t=0}},l={action:"update_section_position",course_id:ps,new_position:r,args:{id_url:vs}};clearTimeout(e),e=setTimeout(()=>{fs.lpSetLoadingEl(i,1),window.lpAJAXG.fetchAJAX(l,a)},1e3)},onMove:t=>{clearTimeout(e)},onUpdate:e=>{t=1}})})(),({$3:Ts,hV:Ls,P0:Is,EO:_s,P9:$s}=t),Ms(),H("sortAbleItem",Ms)})})()})();