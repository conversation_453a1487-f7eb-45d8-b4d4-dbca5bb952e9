(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"course-features extra-box"},(0,e.createElement)("h3",{className:"extra-box__title"},(0,t.__)("Features","learnpress")),(0,e.createElement)("ul",null,(0,e.createElement)("li",null,"Versum metuit commentatus neglegendi turpitudinis putandum pueris discipulum verso dicta hominibus ennius rhetoribus intellegunt"),(0,e.createElement)("li",null,"Repellant tubulo nihilne vester philosophis eo negat exemplis que ullo egone cupido comparatio"),(0,e.createElement)("li",null,"Saepe apparet rerum pollicetur obscurentur clamores instructus petendam accessio possit istarum descensio")))))},n=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-features","title":"Course Features","category":"learnpress-course-elements","icon":"index-card","description":"Renders template Box Extra Features Course PHP templates.","textdomain":"learnpress","keywords":["box extra features single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"align":["wide","full"],"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,i=window.wp.data;let o=null;var u,c,p;u=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],c=l,p=e=>{(0,a.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,i.subscribe)(()=>{const e={...c},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,a.registerBlockType)(l.name,{...l,edit:s,save:n})})();