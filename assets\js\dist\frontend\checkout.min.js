(()=>{"use strict";const e=e=>{if("string"!=typeof e)return e;const t=String.raw({raw:e}).match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);try{e=t?JSON.parse(t[1].replace(/(?:\r\n|\r|\n)/g,"")):JSON.parse(e)}catch(t){e={}}return e};document.addEventListener("submit",e=>{window.lpCheckout.submit(e)}),document.addEventListener("change",e=>{window.lpCheckout.paymentSelect(e)}),document.addEventListener("keyup",e=>{window.lpCheckout.checkEmailGuest(e)}),window.lpCheckout={idFormCheckout:"learn-press-checkout-form",idBtnPlaceOrder:"learn-press-checkout-place-order",classPaymentMethod:"lp-payment-method",classPaymentMethodForm:"payment-method-form",timeOutCheckEmail:null,fetchAPI:(t,o,s)=>{const c={headers:{}};0!==parseInt(lpData.user_id)&&(c.headers["X-WP-Nonce"]=lpData.nonce);const r=new URLSearchParams;Object.keys(o).forEach(e=>{r.append(e,o[e])}),c.method="POST",c.body=r,fetch(t,c).then(e=>e.text()).then(t=>{t=e(t),s.success(t)}).finally(()=>{s.completed()}).catch(e=>s.error(e))},submit:t=>{const o=t.target;if(o.id!==window.lpCheckout.idFormCheckout)return;if(o.classList.contains("processing"))return;t.preventDefault(),o.classList.add("processing");const s=o.querySelector('button[type="submit"]');s.disabled=!0,window.lpCheckout.removeMessage();const c=document.getElementById(window.lpCheckout.idBtnPlaceOrder),r=new URL(lpCheckoutSettings.ajaxurl);r.searchParams.set("lp-ajax","checkout");const n=new FormData(o),a=Object.fromEntries(Array.from(n.keys(),e=>{const t=n.getAll(e);return[e,t.length>1?t:t.pop()]}));c.classList.add("loading");const l={success:t=>{t=e(t);const{message:s,result:c}=t;t.redirect?window.location.href=t.redirect:"success"!==c&&window.lpCheckout.showErrors(o,"error",s)},error:e=>{window.lpCheckout.showErrors(o,"error",e)},completed:()=>{c.classList.remove("loading"),o.classList.remove("processing"),s.disabled=!1}};window.lpCheckout.fetchAPI(r,a,l)},paymentSelect:e=>{const t=e.target.closest(`.${window.lpCheckout.classPaymentMethod}`);if(!t)return;const o=t.closest(".payment-methods");o&&o.querySelectorAll(`.${window.lpCheckout.classPaymentMethod}`).forEach(e=>{const o=e.querySelector(`.${window.lpCheckout.classPaymentMethodForm}`);o&&(o.style.display=t!==e?"none":"block")})},checkEmailGuest:e=>{const t=e.target;"guest_email"===t.id&&window.lpCheckout.isEmail(t.value)&&(t.classList.add("loading"),null!==window.lpCheckout.timeOutCheckEmail&&clearTimeout(window.lpCheckout.timeOutCheckEmail),window.lpCheckout.timeOutCheckEmail=setTimeout(()=>{const e={success:e=>{const{message:o,data:s,status:c}=e;if("success"===c){const e=s.content||"",o=document.querySelector(".lp-guest-checkout-output");o&&o.remove(),t.insertAdjacentHTML("afterend",e)}else window.lpCheckout.showErrors(t.closest("form"),c,o)},error:e=>{window.lpCheckout.showErrors(t.closest("form"),"error",e)},completed:()=>{t.classList.remove("loading")}};window.lpCheckout.fetchAPI(window.location.href,{"lp-ajax":"checkout-user-email-exists",email:t.value},e)},500))},removeMessage:()=>{const e=document.querySelectorAll(".learn-press-message");e&&e.forEach(e=>{e.remove()})},showErrors:(e,t,o)=>{const s=`<div class="learn-press-message ${t}">${o}</div>`;e.insertAdjacentHTML("afterbegin",s),e.scrollIntoView()},isEmail:e=>new RegExp("^[-!#$%&'*+\\./0-9=?A-Z^_`a-z{|}~]+@[-!#$%&'*+\\/0-9=?A-Z^_`a-z{|}~]+.[-!#$%&'*+\\./0-9=?A-Z^_`a-z{|}~]+$").test(e)}})();