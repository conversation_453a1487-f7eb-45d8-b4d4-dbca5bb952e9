(()=>{const{Hook:t}=LP,s=window.jQuery||jQuery;t.addFilter("question-blocks",function(t){return t}),t.addAction("before-start-quiz",function(){}),t.addAction("quiz-started",function(t,e){s(`.course-item-${e}`).removeClass("status-completed failed passed").addClass("has-status status-started"),window.onbeforeunload=function(){return"Warning!"}}),t.addAction("quiz-submitted",function(t,e){s(`.course-item-${e}`).removeClass("status-started passed failed").addClass(`has-status status-completed ${t.results.graduation}`),window.onbeforeunload=null})})();