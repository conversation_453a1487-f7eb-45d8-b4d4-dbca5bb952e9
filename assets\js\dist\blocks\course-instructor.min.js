(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,n=window.wp.components,s=s=>{const l=(0,r.useBlockProps)(),{attributes:a,setAttributes:o,context:i}=s,{lpCourseData:u}=i,c=u?.instructor||"<strong>Instructor</strong>";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.<PERSON>,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.<PERSON>ggle<PERSON>ont<PERSON>,{label:(0,t.__)("Show text 'by'","learnpress"),checked:!!s.attributes.showText,onChange:e=>{s.setAttributes({showText:!!e})}}),(0,e.createElement)(n.<PERSON>,{label:(0,t.__)("Make the instructor a link","learnpress"),checked:!!s.attributes.isLink,onChange:e=>{s.setAttributes({isLink:!!e})}}),s.attributes.isLink?(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Open is new tab","learnpress"),checked:!!s.attributes.target,onChange:e=>{s.setAttributes({target:!!e})}}):"")),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"is-layout-flex c-gap-4"},(0,e.createElement)("label",null,s.attributes.showText?"by ":""),(0,e.createElement)("div",{className:"course-instructor"},s.attributes.isLink?(0,e.createElement)("a",{dangerouslySetInnerHTML:{__html:c}}):(0,e.createElement)("div",{dangerouslySetInnerHTML:{__html:c}})))))},l=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-instructor","title":"Course Instructor","category":"learnpress-course-elements","description":"Renders template Instructor Course PHP templates.","textdomain":"learnpress","keywords":["instructor single course","learnpress"],"icon":"id","ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"showText":{"type":"boolean","default":true},"isLink":{"type":"boolean","default":true},"target":{"type":"boolean","default":false}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":true,"__experimentalDefaultControls":{"link":false,"text":true}}}}'),o=window.wp.blocks,i=window.wp.data;let u=null;var c,p,m;c=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],p=a,m=e=>{(0,o.registerBlockType)(e.name,{...e,edit:s,save:l})},(0,i.subscribe)(()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&u!==r&&(u=r,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))}),(0,o.registerBlockType)(a.name,{...a,edit:s,save:l})})();