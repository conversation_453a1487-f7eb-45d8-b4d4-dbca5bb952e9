document.addEventListener("DOMContentLoaded",function(){const e=window.jQuery,t=document.getElementById("current-material-post-id").value,a=document.getElementById("material-max-file-size").value,l=document.querySelector(".lp-material--field-upload").getAttribute("accept").split(","),n=document.getElementById("available-to-upload"),i=document.getElementById("btn-lp--add-material"),r=document.getElementById("lp-material--add-material-template"),o=document.getElementById("lp-material--group-container"),s=document.getElementById("lp-material-container"),c=document.getElementById("btn-lp--save-material"),d=(e,t)=>{if(!e)return;const a=document.getElementById("delete-material-row-text").value;e.insertAdjacentHTML("beforeend",`<tr data-id="${t.file_id}" data-sort="${t.orders}" >\n              <td class="sort"><span class="dashicons dashicons-menu"></span> ${t.file_name}</td>\n              <td>${m(t.method)}</td>\n              <td><a href="javascript:void(0)" class="delete-material-row" data-id="${t.file_id}">${a}</a></td>\n            </tr>`)},m=e=>e.charAt(0).toUpperCase()+e.substring(1);function u(e,a=!1,l){if(e.length>0){let r=[],s=new FormData,c=!0;if(e.forEach(function(e,t){const a=e.querySelector(".lp-material--field-title").value,l=e.querySelector(".lp-material--field-method").value,n=e.querySelector(".lp-material--field-external-link"),i=e.querySelector(".lp-material--field-upload");let o,d;switch(a||(c=!1),l){case"upload":i.value?(o=i.files[0].name,d="",s.append("file[]",i.files[0])):c=!1;break;case"external":d=n.value,o="",d||(c=!1)}r.push({label:a,method:l,file:o,link:d})}),c){r=JSON.stringify(r);const c=`${lpGlobalSettings.rest}lp/v1/material/item-materials/${t}`;s.append("data",r),l.classList.add("loading"),fetch(c,{method:"POST",headers:{"X-WP-Nonce":lpGlobalSettings.nonce},body:s}).then(e=>e.text()).then(t=>{a?e[0].remove():o.innerHTML="";const l=JSON.parse(t),{message:r,data:s,status:c}=l;if(alert(r),"success"===c&&s.length>0){const e=document.querySelector(".lp-material--table"),t=e.querySelector("thead"),a=e.querySelector("tbody");t.classList.remove("hidden");for(let e=0;e<s.length;e++){const t=s[e];d(a,t)}n.innerText=parseInt(n.innerText)-s.length,i.setAttribute("can-upload",n.innerText)}}).finally(()=>{l.classList.remove("loading")}).catch(e=>console.log(e))}else alert("Enter file title, choose file or enter file link!")}}(async(e,t)=>{const a=document.querySelector(".lp-material--table tbody");try{const l=`${lpDataAdmin.lp_rest_url}lp/v1/material/item-materials/${t}`;fetch(l,{method:"GET",headers:{"X-WP-Nonce":lpGlobalSettings.nonce,"Content-Type":"application/json"}}).then(e=>e.json()).then(t=>{const{data:l,status:n}=t;if("success"===n){if(l&&l.items&&l.items.length>0){const t=l.items;e.querySelector(".lp-skeleton-animation")&&e.querySelector(".lp-skeleton-animation").remove();for(let e=0;e<t.length;e++)d(a,t[e])}}else console.error(t.message)}).catch(e=>console.log(e))}catch(e){console.log(e.message)}})(s,t),i.addEventListener("click",function(e){const t=~~this.getAttribute("can-upload");if(o.querySelectorAll(".lp-material--group").length>=t)return!1;o.insertAdjacentHTML("afterbegin",r.innerHTML)}),s.addEventListener("change",function(e){const t=e.target;if(t.classList.contains("lp-material--field-method")){const e=t.value,a=document.getElementById("lp-material--upload-field-template").innerHTML,l=document.getElementById("lp-material--external-field-template").innerHTML;switch(e){case"upload":t.parentNode.insertAdjacentHTML("afterend",a),t.closest(".lp-material--group").querySelector(".lp-material--external-wrap").remove();break;case"external":t.parentNode.insertAdjacentHTML("afterend",l),t.closest(".lp-material--group").querySelector(".lp-material--upload-wrap").remove()}}t.classList.contains("lp-material--field-upload")&&t.value&&t.files.length>0&&(l.includes(t.files[0].type)?t.files[0].size>1024*a*1024&&(alert(`This file size is greater than ${a}MB! Please choose another file!`),t.value=""):(alert("This file is not allowed! Please choose another file!"),t.value=""))}),s.addEventListener("click",function(e){const t=e.target;if(t.classList.contains("lp-material--delete")&&"BUTTON"==t.nodeName)t.closest(".lp-material--group").remove();else if(t.classList.contains("lp-material-save-field")){let e=t.closest(".lp-material--group");e=p(e),u(e,!0,t)}return!1}),c.addEventListener("click",function(e){const t=o.querySelectorAll(".lp-material--group");t.length>0&&u(t,!1,c)}),document.addEventListener("click",function(e){const a=e.target;if(a.classList.contains("delete-material-row")&&"A"==a.nodeName){const e=a.getAttribute("data-id"),l=document.getElementById("delete-material-message").value;confirm(l)&&wp.apiFetch({path:`lp/v1/material/${e}`,method:"DELETE",data:{item_id:t}}).then(e=>{200===e.status&&e.delete?(a.closest("tr").remove(),n.innerText=1+~~n.innerText,i.setAttribute("can-upload",~~n.innerText)):alert(e.message)}).catch(e=>{console.log(e)}).finally(()=>{})}});const p=(f=document.createDocumentFragment().childNodes,e=>{const t={0:{value:e,enumerable:!0},length:{value:1},item:{value(e){return this[+e||0]},enumerable:!0}};return Object.create(f,t)});var f;e(".lp-material--table tbody").sortable({items:"tr",cursor:"move",axis:"y",handle:"td.sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,update(a,l){e(this).sortable("option","disabled",!0),function(){const a=e(".lp-material--table tbody tr"),l=[];a.each(function(t,a){e(this).attr("data-sort",t+1),l.push({file_id:~~e(this).attr("data-id"),orders:t+1})}),wp.apiFetch({path:`lp/v1/material/item-materials/${t}`,method:"PUT",data:{sort_arr:JSON.stringify(l)}}).then(e=>{200==e.status||alert("Sort table fail.")}).catch(e=>{console.log(e)}).finally(()=>{})}(),e(this).sortable("option","disabled",!1)}})});