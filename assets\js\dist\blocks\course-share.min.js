(()=>{"use strict";const e=window.React,r=window.wp.i18n,s=window.wp.blockEditor,n=n=>{const t=(0,s.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...t},(0,e.createElement)("div",{className:"is-layout-flex c-gap-4"},(0,e.createElement)("i",{className:"lp-icon-share-alt"}),(0,e.createElement)("span",null,(0,r.__)("Share","learnpress")))))},t=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-share","title":"Course Share","category":"learnpress-course-elements","icon":"share","description":"Renders Share Course Block PHP Template.","textdomain":"learnpress","keywords":["share course single","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":true,"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,o=window.wp.data;let c=null;var i,u,p;i=["learnpress/learnpress//single-lp_course","learnpress/learnpress//single-lp_course-offline"],u=l,p=e=>{(0,a.registerBlockType)(e.name,{...e,edit:n,save:t})},(0,o.subscribe)(()=>{const e={...u},r=(0,o.select)("core/editor")||null;if(!r||"function"!=typeof r.getCurrentPostId||!r.getCurrentPostId())return;const s=r.getCurrentPostId();null!==s&&c!==s&&(c=s,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),i.includes(s)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))}),(0,a.registerBlockType)(l.name,{...l,edit:n,save:t})})();