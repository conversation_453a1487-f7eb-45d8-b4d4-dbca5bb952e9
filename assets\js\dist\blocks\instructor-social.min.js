(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,l=window.wp.components,r=r=>{const s=(0,n.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.<PERSON>s,null,(0,e.createElement)(l.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.<PERSON>,{label:(0,t.__)("Open links in new tab","learnpress"),checked:!!r.attributes.target,onChange:e=>{r.setAttributes({target:!!e})}}),(0,e.createElement)(l.<PERSON>ont<PERSON>,{label:(0,t.__)("Add nofollow attribute","learnpress"),checked:!!r.attributes.nofollow,onChange:e=>{r.setAttributes({nofollow:!!e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"instructor-social"},(0,e.createElement)("i",{className:"lp-user-ico lp-icon-facebook"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-twitter"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-youtube-play"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-linkedin"}))))},s=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-social","title":"Instructor Social","category":"learnpress-course-elements","icon":"share","description":"Renders template Instructor Social PHP templates.","textdomain":"learnpress","keywords":["instructor social single","learnpress"],"ancestor":["learnpress/single-instructor"],"attributes":{"target":{"type":"boolean","default":false},"nofollow":{"type":"boolean","default":false}},"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),a=window.wp.blocks,i=window.wp.primitives,c=window.ReactJSXRuntime,p=(0,c.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(i.Path,{d:"M12.5 14.5h-1V16h1c2.2 0 4-1.8 4-4s-1.8-4-4-4h-1v1.5h1c1.4 0 2.5 1.1 2.5 2.5s-1.1 2.5-2.5 2.5zm-4 1.5v-1.5h-1C6.1 14.5 5 13.4 5 12s1.1-2.5 2.5-2.5h1V8h-1c-2.2 0-4 1.8-4 4s1.8 4 4 4h1zm-1-3.2h5v-1.5h-5v1.5zM18 4H9c-1.1 0-2 .9-2 2v.5h1.5V6c0-.3.2-.5.5-.5h9c.3 0 .5.2.5.5v12c0 .3-.2.5-.5.5H9c-.3 0-.5-.2-.5-.5v-.5H7v.5c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"})}),u=window.wp.data;let m=null;const d=[Number(lpDataAdmin?.single_instructor_id)];var w,g,f;w=d,g=o,f=e=>{(0,a.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,u.subscribe)(()=>{const e={...g},t=(0,u.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&m!==n&&(m=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),w.includes(n)?(e.ancestor=null,f(e)):(e.ancestor||(e.ancestor=[]),f(e))))}),(0,a.registerBlockType)(o.name,{...o,icon:p,edit:r,save:s})})();